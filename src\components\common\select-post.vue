<template>
  <wd-popup v-model="modelShow" custom-class=" rounded-[20rpx_20rpx_0_0]" position="bottom">
    <view class="h-700rpx">
      <z-paging
        ref="pagingRef"
        v-model="pageData"
        :fixed="false"
        :paging-style="pagePostStyle"
        :refresher-enabled="false"
        :show-loading-more-no-more-view="false"
        auto
        safe-area-inset-bottom
        @query="queryList"
      >
        <view class="flex flex-col px-36rpx pt-32rpx">
          <!-- <view
            v-for="(item, key) in pageData"
            :key="key"
            class="px-34rpx py-14rpx bg-#383838 shadow-[0rpx_20rpx_70rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx flex flex-col gap-10rpx border-1px border-solid border-#f9f9f9"
            @click="handleSelectReleasePost(item)"
          >
            <text class="c-#ffcb62 text-28rpx font-500">{{ item.positionName }}</text>
            <view class="grid grid-cols-3 gap-22rpx">
              <view
                v-for="(tag, index) in item.positionKeyList"
                :key="index"
                class="c-#ffcb62 text-22rpx center h-36rpx bg-#383838 rounded-6rpx border-1px border-solid border-#f9f9f9"
              >
                {{ tag }}
              </view>
            </view>
          </view> -->
          <view
            v-for="(item, key) in pageData"
            :key="key"
            class="px-34rpx py-26rpx bg-#383838 rounded-20rpx flex flex-col gap-10rpx"
            @click="handleSelectReleasePost(item)"
          >
            <view class="flex items-center justify-between">
              <text
                :class="isSelected(item) ? 'c-#ffcb62' : 'c-#9f9f9f'"
                class="text-32rpx font-500 py-10rpx"
              >
                {{ item.positionMarkName ? item.positionMarkName : item.positionName }}
              </text>
              <image
                v-show="isSelected(item)"
                src="@/static/deepseek/business/selected-Icon.png"
                class="w-32rpx h-32rpx"
              />
            </view>
            <view class="divider"></view>
          </view>
        </view>
      </z-paging>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { hrPositionQueryOptionList } from '@/service/hrPosition'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'

interface EmitsInterface {
  (e: 'select-post', item: hrPositionQueryOptionListInt): void
}

interface PropsInterface {
  selectedPost?: hrPositionQueryOptionListInt | null
}

const emit = defineEmits<EmitsInterface>()
const props = defineProps<PropsInterface>()
const modelShow = defineModel('show', {
  type: Boolean,
  default: false,
})
const {
  pagingRef,
  pageInfo,
  pageSetInfo,
  pageStyle: pagePostStyle,
  pageData,
} = usePaging<hrPositionQueryOptionListInt>({
  style: {
    borderRadius: '0',
    background: '#383838',
  },
})

async function fetchPagingHrPositionQueryOptionList() {
  const { data } = await hrPositionQueryOptionList({
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}

function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  fetchPagingHrPositionQueryOptionList()
}

function handleSelectReleasePost(item: hrPositionQueryOptionListInt) {
  emit('select-post', item)
}

function isSelected(item: hrPositionQueryOptionListInt) {
  return props.selectedPost?.id === item.id
}
</script>

<style lang="scss" scoped>
//

.divider {
  height: 1rpx;
  margin-top: 10rpx;
  background: #9f9f9f;
}
</style>
