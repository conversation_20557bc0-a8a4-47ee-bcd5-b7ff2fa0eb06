<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    :fixed="false"
    :paging-style="pageStyle"
    :refresher-enabled="false"
    :show-loading-more-no-more-view="false"
    auto
    @query="queryList"
  >
    <view class="flex flex-col pt-30rpx">
      <view
        v-for="(item, key) in pageData"
        :key="key"
        :class="{ 'selected-post': isSelectedPost(item) }"
        class="px-52rpx py-40rpx flex flex-col gap-22rpx border-b-1 border-b-solid border-b-#D9D9D9"
        @tap="handleSelectPost(item)"
      >
        <view class="flex items-center justify-between">
          <view class="flex flex-col">
            <text class="c-#000000 text-30rpx line-clamp-1">
              {{ item.positionMarkName ? item.positionMarkName : item.positionName }}
            </text>
            <!-- 选中岗位的横线标识 -->
            <view v-if="isSelectedPost(item)" class="selected-line"></view>
          </view>

          <text class="c-#000000 text-30rpx line-clamp-1 text-right">
            {{ handleSalary(item) }}
          </text>
        </view>

        <text class="c-#555555 text-24rpx">
          <wd-img :height="12" :src="position" :width="12" class="m-r-6rpx" />
          {{ formatAddress(item) }}
        </text>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { EMIT_EVENT } from '@/enum'
import { hrPositionQueryIndexOptionList } from '@/service/hrPosition'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { hrPositionQueryIndexOptionListInt } from '@/service/hrPosition/types'
import position from '@/setting/img/position.png'

const $emits = defineEmits<{
  (e: 'selectPost'): void
}>()
const { releaseActivePost } = useReleasePost()
const { pagingRef, pageInfo, pageSetInfo, pageStyle, pageData } =
  usePaging<hrPositionQueryIndexOptionListInt>({
    style: {},
  })

function formatAddress(item: hrPositionQueryIndexOptionListInt) {
  const { provinceName, cityName, districtName } = item
  const isDirectMunicipality = provinceName === cityName
  const addressParts = isDirectMunicipality
    ? [provinceName, districtName]
    : [provinceName, cityName, districtName]
  return addressParts.filter(Boolean).join('')
}

async function fetchPagingHrPositionQueryIndexOptionList() {
  await uni.$onLaunched
  const { data } = await hrPositionQueryIndexOptionList({
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}

function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  fetchPagingHrPositionQueryIndexOptionList()
}

// 处理薪资范围
const handleSalary = (item: hrPositionQueryIndexOptionListInt): string => {
  const { workSalaryBegin, workSalaryEnd } = item

  if (!workSalaryBegin && !workSalaryEnd) {
    return '面议'
  }

  const beginK = (workSalaryBegin / 1000).toFixed(1).replace(/\.0$/, '')
  const endK = (workSalaryEnd / 1000).toFixed(1).replace(/\.0$/, '')
  return `${beginK}k-${endK}k/月`
}

function handleSelectPost(item: hrPositionQueryIndexOptionListInt) {
  releaseActivePost.value = item
  $emits('selectPost')
}
function isSelectedPost(item: hrPositionQueryIndexOptionListInt): boolean {
  return releaseActivePost.value?.id === item.id
}
function reload() {
  pagingRef.value?.reload()
}
uni.$on(EMIT_EVENT.REFRESH_PUBLISH_POSITION, reload)
onBeforeUnmount(() => {
  uni.$off(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
})
</script>

<style lang="scss" scoped>
:deep(.zp-view-super) {
  margin: 0 !important;
}

:deep(.zp-paging-container-content) {
  height: auto !important;
}

// 选中岗位的样式
.selected-post {
  position: relative;
  background-color: #f8f9fa;
}

// 选中岗位的横线标识
.selected-line {
  height: 8rpx;
  margin-top: 4rpx;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 8px;
}
</style>
