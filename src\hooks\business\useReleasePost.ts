import {
  type hrPositionQueryOptionListInt,
  type hrPositionQueryIndexOptionListInt,
  PositionStatus,
} from '@/service/hrPosition/types'

interface releaseActivePostInt extends hrPositionQueryOptionListInt, Record<string, any> {}
interface releaseActiveIndexPostInt
  extends hrPositionQueryIndexOptionListInt,
    Record<string, any> {}

const releaseActivePost = ref<releaseActiveIndexPostInt>({})
const releaseSeniorPostActivePost = ref<releaseActivePostInt>({})
const releasePropPostActivePost = ref<releaseActivePostInt>({})
/** 发布岗位hooks */
export const useReleasePost = () => {
  const releaseIsHavePost = computed(() => !!releaseActivePost.value?.id)
  const releaseActivePostIsEnabled = computed(
    () => releaseIsHavePost.value && releaseActivePost.value?.status !== PositionStatus.RECRUITING,
  )
  const releaseIsHaveSeniorPost = computed(() => !!releaseSeniorPostActivePost.value?.id)
  const releaseIsHavePropPost = computed(() => !!releasePropPostActivePost.value?.id)
  return {
    releaseActivePost,
    releaseIsHavePost,
    releaseActivePostIsEnabled,
    releaseSeniorPostActivePost,
    releaseIsHaveSeniorPost,
    releasePropPostActivePost,
    releaseIsHavePropPost,
  }
}
