import { ChatUIKit } from '@/ChatUIKit'
import websdk from 'easemob-websdk/uniApp/Easemob-chat'
import { EasemobChatStatic } from 'easemob-websdk/Easemob-chat'
import { IM_LISTENER_TYPE } from '@/enum'
import { imDomainInfo, imCreateAccount } from '@/service/im'

/** 环信息IM hooks */
export const useEaseMobIM = () => {
  const { getUserIsLogin, userIntel, isCompleteBigStep } = useUserInfo()
  const {
    listenForNewMessages,
    loadConversations,
    processHistoryMarkSyncMessages,
    listenForPushNotifications,
    closePushNotifications,
  } = useIMConversation()
  /** 初始化IM */
  const initEaseMobIM = async () => {
    await uni.$onLaunched
    if (!getUserIsLogin.value || !isCompleteBigStep.value) return
    const { data } = await imDomainInfo()
    const { appKey, basePath: apiUrl, baseWsPath } = data
    // eslint-disable-next-line new-cap
    const chat = new (websdk as unknown as EasemobChatStatic).connection({
      appKey,
      url: `${baseWsPath[Math.floor(Math.random() * baseWsPath.length)]}/websocket`,
      apiUrl,
      isHttpDNS: true,
      delivery: true,
    })
    ChatUIKit.init({
      chat,
      config: {
        theme: {
          avatarShape: 'circle',
        },
        isDebug: true,
      },
    })
    uni.$UIKit = ChatUIKit
    eventHandlerConnection()
    listenForNewMessages()
    loginEaseMobIM(userIntel.value.type)
  }
  /** 登录IM */
  const loginEaseMobIM = async (type: Api.Common.USER_TYPE) => {
    try {
      closePushNotifications()
      const { data } = await imCreateAccount(
        {
          type,
        },
        {
          custom: {
            loading: true,
            catch: true,
          },
        },
      )
      const { username: user, accessToken } = data
      await uni.$UIKit.chatStore.login({
        user,
        accessToken,
      })
      listenForPushNotifications()
      await loadConversations(true)
      processHistoryMarkSyncMessages()
    } catch (error) {
      console.error('IM登录失败:', error)
    }
  }
  /** 登出IM */
  const logoutEaseMobIM = () => {
    if (uni.$UIKit && uni.$UIKit?.chatStore) {
      uni.$UIKit.chatStore.logout()
    }
  }
  /** 监听连接状态 */
  const eventHandlerConnection = () => {
    uni.$UIKit.getChatConn().addEventHandler(IM_LISTENER_TYPE.CONNECTION_STATUS, {
      onConnected: () => {
        console.log('连接成功')
      },
      onDisconnected: (error) => {
        console.log('连接断开', error)
      },
      onReconnecting: () => {
        console.log('重连中')
      },
    })
  }

  const receiveMessage = () => {
    console.log('Message received')
  }

  return {
    initEaseMobIM,
    loginEaseMobIM,
    logoutEaseMobIM,
    receiveMessage,
  }
}
