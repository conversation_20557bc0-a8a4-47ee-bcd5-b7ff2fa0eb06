import { SensitiveFilter } from '@/utils/SensitiveFilter'

export function useSensitiveFilter() {
  const filter = ref<SensitiveFilter | null>(null)
  const isLoading = ref(true)
  const isReady = ref(false)

  const initFilter = async () => {
    try {
      isLoading.value = true
      const words = []
      filter.value = new SensitiveFilter(words)
      isReady.value = true
    } catch (error) {
      console.error('初始化敏感词过滤器失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 标准模式过滤
   */
  const filterText = (text: string, replacement: string = '*'): string => {
    if (!filter.value || !isReady.value) return text
    return filter.value.filterText(text, replacement)
  }

  /**
   * 严格模式过滤（包含变形检测）
   */
  const filterTextStrict = (text: string, replacement: string = '*'): string => {
    if (!filter.value || !isReady.value) return text
    return filter.value.filterTextStrict(text, replacement)
  }

  /**
   * 检查是否包含敏感词
   */
  const containsSensitiveWord = (text: string): boolean => {
    if (!filter.value || !isReady.value) return false
    return filter.value.containsSensitiveWord(text)
  }

  /**
   * 获取详细的敏感词信息
   */
  const getSensitiveWords = (text: string) => {
    if (!filter.value || !isReady.value) return []
    return filter.value.getSensitiveWords(text)
  }

  onMounted(() => {
    initFilter()
  })

  return {
    isLoading,
    isReady,
    initFilter,
    filterText,
    filterTextStrict,
    containsSensitiveWord,
    getSensitiveWords,
  }
}
