import { POSTPaging, POST } from '../index'
import { hrIndexResumeUserListDataInt, hrIndexResumeUserListInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 简历用户列表查询接口 */
export const hrIndexResumeUserList = (
  data: Api.Request.IResPagingDataParamsInt<hrIndexResumeUserListDataInt>,
  config?: HttpRequestConfig,
) => POSTPaging<hrIndexResumeUserListInt>('/easyzhipin-api/hrIndex/resumeUserList', data, config)

/** 判断是否可以发送消息 */
export const hrIndexCanSendMsg = (config?: HttpRequestConfig) =>
  POST<Api.Common.EnableStatus>('/easyzhipin-api/hrIndex/canSendMsg', {}, config)
