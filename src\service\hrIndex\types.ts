import { JobType, SeekStatus, Gender, QualificationType } from '@/enum'

export interface hrIndexResumeUserListDataInt {
  /** 城市code */
  cityCode?: string
  /** 区code */
  districtCode?: string
  /** HR当前所选的岗位id */
  positionInfoId: number
  /** 省份code */
  provinceCode?: string
  /** 学历等级 */
  qualification?: number
  /** 期望薪资截止(元)，选不限时别送 */
  salaryExpectationEnd?: number
  /** 期望薪资开始(元),选不限时别送 */
  salaryExpectationStart?: number
  /** 查询类型0推荐1最新 */
  searchType?: Api.Common.EnableStatus
  /** 求职状态 */
  seekStatus?: SeekStatus
  /** 工作类型 */
  jobType?: JobType
}
export interface hrIndexResumeUserListInt
  extends Api.Common.Record<{
    /* 在线情况 */
    ActivityStatus?: number
    /** 竞争力 */
    activityTotal?: number
    /** 证书名逗号分割 */
    certificateNames?: string
    /** 城市code */
    cityCode?: string
    /** 区code */
    districtCode?: string
    /** 期望岗位 */
    expectedPositions?: string
    /** 期望岗位code */
    expectedPositionsCode?: string
    /** 期望岗位parent */
    expectedPositionsParent?: string
    /** 首次参加工作时间 */
    firstJobDate?: string
    /** 工作经历 */
    workExperienceList?: Array<{
      /** 公司名称 */
      workCompanyName?: string
      /** 岗位名称 */
      workPositionName?: string
      /** 工作年限 */
      workYears?: number
    }>
    /** 专业 */
    major?: string
    /** 工作类型 1 全职 2 兼职 3 实习 */
    jobType?: JobType
    /** 我的亮点 */
    myLights?: string
    /** 期望薪资开始(元) */
    provinceCode?: string
    /** 学历等级 枚举字典id=10 */
    qualification?: number
    /** 0非全日制1全日制 */
    qualificationType?: QualificationType
    /** 更改简历后的刷新时间 */
    refreshTime?: string
    /** 简历头像ID */
    resumeAvatarId?: string
    /** 简历主信息ID */
    resumeBaseInfoId?: number
    /** 期望薪资开始(元) */
    salaryExpectationEnd?: number
    /** 期望薪资开始(元) */
    salaryExpectationStart?: number
    /** 学校 */
    school?: string
    /** 求职状态 */
    seekStatus?: SeekStatus
    /** 性别 */
    sex?: Gender
    /** 掌握技能 逗号分割 */
    skills?: string
    /** 真实姓名 */
    trueName?: string
    /** 用户id */
    userId?: number
  }> {}
