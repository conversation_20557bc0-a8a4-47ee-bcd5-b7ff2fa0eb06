import { POST } from '../index'
import { HttpRequestConfig } from 'luch-request'
import { addInvoiceRecordType } from './type'

/** 新增发票申请 */
export const addInvoiceRecord = (data: addInvoiceRecordType, config?: HttpRequestConfig) =>
  POST<string>('/easyzhipin-api/hrInvoiceRecord/add', data, config)

/** 通过Id查看发票申请 */
export const getInvoiceRecordById = (data: any, config?: HttpRequestConfig) =>
  POST<string>(`/easyzhipin-api/hrInvoiceRecord/queryById`, data, config)

/** 查询发票申请列表 */
export const getInvoiceRecordList = (data: any, config?: HttpRequestConfig) =>
  POST<string>(`/easyzhipin-api/hrInvoiceRecord/queryList`, data, config)

// 获取公司信息
export const getCompanyInfoById = (config?: HttpRequestConfig) =>
  POST<string>(`/easyzhipin-api/hrCompany/queryCompanyById`, {}, config)
