<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="个人信息清单"></CustomNavBar>
    <view class="setting">
      <view class="setting-list border-b flex-between" @click="goBasicInfo">
        <view class="list-item-text text-32rpx">基本信息</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list border-b flex-between" @click="goIdentityInfo">
        <view class="list-item-text text-32rpx">身份信息</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <!--      <view class="setting-list border-b flex-between" @click="goDeviceInfo">-->
      <!--        <view class="list-item-text text-32rpx">设备信息</view>-->
      <!--        <wd-icon-->
      <!--          name="chevron-right"-->
      <!--          size="20px"-->
      <!--          color="#888888"-->
      <!--          class="arrow-right-icon"-->
      <!--        ></wd-icon>-->
      <!--      </view>-->
      <view class="setting-list border-b flex-between">
        <view class="list-item-text text-32rpx">使用过程信息</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 基本信息
const goBasicInfo = () => {
  uni.navigateTo({
    url: '/setting/PersonalInfoList/basicInfo',
  })
}

// 身份信息
const goIdentityInfo = () => {
  uni.navigateTo({
    url: '/setting/PersonalInfoList/IdentityInfo',
  })
}

// 设备信息
const goDeviceInfo = () => {
  uni.navigateTo({
    url: '/setting/loginDevice/index',
  })
}
</script>
<style lang="scss" scoped>
.setting {
  padding: 0rpx 40rpx;

  .setting-list {
    padding: 30rpx 20rpx;

    .list-item-text {
      color: #333;
    }
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx;
}

::v-deep .u-cell__value {
  font-size: 28rpx;
  color: #888888;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}
</style>
