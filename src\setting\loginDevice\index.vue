<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="登录设备管理"></CustomNavBar>
    <view class="setting">
      <view class="setting-text m-b-40rpxrpx">
        以下是最近登录过您账号的设备情况，若发现非本人操作，请及时删除，以保障您的账号安全
      </view>

      <z-paging
        ref="pagingRef"
        v-model="pageData"
        :fixed="false"
        :paging-style="pageStyle"
        :style="{ height: `calc(100vh - ${customBar * 2}rpx - 200rpx)` }"
        safe-area-inset-bottom
        @query="queryList"
      >
        <view>
          <!-- 当前设备显示 -->
          <view
            v-if="currentDevice && (currentDevice.deviceBrand || currentDevice.deviceModel)"
            class="setting-list border-b"
          >
            <view class="flex-between">
              <view class="text-32rpx font-w-500">
                {{ `${currentDevice?.deviceBrand || ''} ${currentDevice?.deviceModel || ''}` }}
              </view>
              <view class="color-8">当前设备</view>
            </view>
            <view class="color-8">{{ currentDevice?.updateTime || '' }}</view>
          </view>

          <!-- 历史设备列表 -->
          <template v-if="pageData && pageData.length > 0">
            <view v-for="(item, index) in pageData" :key="index" class="setting-list border-b">
              <view class="flex-between">
                <view class="text-32rpx font-w-500">
                  {{ `${item?.deviceBrand || ''} ${item?.deviceModel || ''}` }}
                </view>
              </view>
              <view class="color-8">{{ item?.updateTime || '' }}</view>
            </view>
          </template>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryLoginDeviceList } from '@/interPost/my'
import { getCustomBar } from '@/utils/storage'

const customBar = ref(null)

// 初始化分页
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    padding: '0rpx',
    background: 'transparent',
  },
})

const params = ref({
  entity: {
    dealState: null,
  },
  orderBy: {},
  page: 1,
  size: 10,
})

const currentDevice = ref<any>({}) // 当前设备信息

// 分页查询列表
const queryList = async (page: number, size: number) => {
  try {
    pageSetInfo(page, size)
    const res: any = await queryLoginDeviceList({
      ...params.value,
      page: pageInfo.page,
      size: pageInfo.size,
    })

    if (res.code === 0 && Array.isArray(res.data?.list)) {
      const now = new Date().getTime()

      // 按时间差从小到大排序（时间越接近现在的排前面）
      const sortedList = res.data.list.sort((a: any, b: any) => {
        const diffA = Math.abs(new Date(a.updatatime).getTime() - now)
        const diffB = Math.abs(new Date(b.updatatime).getTime() - now)
        return diffA - diffB
      })

      // 设置分页数据
      pageData.value = sortedList || []

      // 设置当前设备（第一页的第一条数据）
      if (page === 1 && sortedList.length > 0) {
        currentDevice.value = sortedList[0] || {}
      }
    }
    pagingRef.value.complete(res.data.list)
  } catch (error) {
    console.error('查询登录设备列表失败:', error)
  }
}

onLoad(async () => {
  await uni.$onLaunched
  customBar.value = await getCustomBar()
})
</script>
<style lang="scss" scoped>
.setting {
  padding: 40rpx 40rpx;

  .setting-list {
    padding: 30rpx 0rpx;
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx 40rpx;
}

::v-deep .u-cell__value {
  font-size: 28rpx;
  color: #888888;
}

::v-deep .u-cell__label--large {
  font-size: 28rpx;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}

.setting-text {
  font-size: 24rpx;
  color: #888888;
}
</style>
