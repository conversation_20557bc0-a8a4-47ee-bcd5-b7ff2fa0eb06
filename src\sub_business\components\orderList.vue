<template>
  <view
    @click="handleClick"
    class="shadow-[0px_4px_13.5px_0px_rgba(0,0,0,0.15)] px-40rpx py-30rpx bg-white rounded-20rpx relative m-b-20rpx"
  >
    <view class="flex items-baseline">
      <view class="c-#000 text-32rpx font500 p-b-15rpx">
        {{ truncateText(item.goodsDesc, 8) }}
      </view>
      <view class="c-#666 text-28rpx font500 m-l-10rpx">￥{{ item.actualAmount / 100 }}</view>
    </view>

    <view class="flex items-center p-b-20rpx">
      <view class="c-#333 text-26rpx">岗位：</view>
      <view class="c-#333 text-26rpx">{{ item.positionName }}</view>
    </view>
    <view class="flex items-center p-b-20rpx">
      <view class="c-#333 text-26rpx">订单号：</view>
      <view class="c-#333 text-26rpx">{{ item.outTradeNo }}</view>
    </view>
    <view class="flex items-center p-b-20rpx" v-if="item.dealCreateTime">
      <view class="c-#333 text-26rpx">下单时间：</view>
      <view class="c-#333 text-26rpx">{{ item.dealCreateTime }}</view>
    </view>
    <view class="flex items-center" v-if="item.finishTime">
      <view class="c-#333 text-26rpx">到期时间：</view>
      <view class="c-#333 text-26rpx">{{ item.finishTime }}</view>
    </view>
    <view class="flex items-center" v-if="item.dealState">
      <view class="absolute top-30rpx right-40rpx c-#555 text-32rpx">
        {{ item.dealStateName }}
      </view>
    </view>
    <view
      v-if="!item.dealState && item.finishTimeD > 0"
      class="absolute top-30rpx right-40rpx flex items-baseline"
    >
      <wd-count-down :time="item.finishTimeD" format="mm:ss" />
      <view class="m-l-10rpx c-#555 text-32rpx c-#ff4545">
        {{ item.dealStateName }}
      </view>
    </view>
    <view class="flex items-center" v-if="!item.dealState && item.finishTimeD <= 0">
      <view class="absolute top-30rpx right-40rpx c-#555 text-32rpx">支付过期</view>
    </view>
    <view
      @click.stop="handlePay(item.dealId, item.outTradeNo)"
      v-if="!item.dealState && item.finishTimeD > 0"
      class="bg-#5882FF rounded-10rpx h-70rpx c-#fff w-180rpx text-26rpx text-center line-height-70rpx absolute bottom-30rpx right-40rpx"
    >
      立即支付
    </view>
    <view
      @click.stop="handleCancel(item.dealId)"
      v-if="!item.dealState && item.finishTimeD <= 0"
      class="bg-#5882FF rounded-10rpx h-70rpx c-#fff w-180rpx text-26rpx text-center line-height-70rpx absolute bottom-30rpx right-40rpx"
    >
      删除订单
    </view>
  </view>
  <wd-message-box />
</template>

<script setup lang="ts">
import { payDealStatus, cancelDealStatus } from '@/service/order'
import { truncateText } from '@/utils/util'
import { payQueryDealStatus } from '@/service/pay'
import { useToast, useMessage } from 'wot-design-uni'

const toast = useToast()
const message = useMessage()
const pollTimer = ref<number | null>(null)
const { setFalse: setShowPaymentMethod } = useBoolean()
const time = ref(600 * 60 * 24 * 30)
const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
})
const handleClick = () => {
  uni.navigateTo({
    url: `/sub_business/pages/order/detail?dealId=${props.item.dealId}&dealState=${props.item.dealState}`,
  })
}
// 取消订单
const handleCancel = async (dealId: number) => {
  message
    .confirm({
      title: '提示',
      msg: '确定删除订单吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(async () => {
      const res: any = await cancelDealStatus({ dealId })
      if (res.code === 0) {
        toast.show('删除订单成功')
        uni.$emit('paySuccess')
      }
    })
}
const handlePay = async (dealId: number, outTradeNo: string) => {
  const res: any = await payDealStatus({ dealRecordId: dealId })
  console.log(res)
  if (res.code === 0) {
    await handlePayment(res.data, outTradeNo)
  }
}

// 支付函数
const payConnetMethod = (orderStr: any) => {
  return new Promise((resolve, reject) => {
    console.log(orderStr, 'orderStr===========111===')
    let payType = 1
    if (orderStr?.orderStr) {
      payType = 2
    }
    uni.requestPayment({
      provider: payType === 1 ? 'wxpay' : 'alipay',
      orderInfo: payType === 1 ? orderStr : orderStr.orderStr,
      success(res) {
        resolve(res)
      },
      fail(e) {
        reject(e)
      },
    })
  })
}
const handlePayment = async (orderStr: any, outTradeNo: string) => {
  try {
    toast.loading({
      msg: '支付中...',
      loadingType: 'outline',
      duration: 0,
      cover: true,
    })
    const res = await payConnetMethod(orderStr)
    console.log(res, 'res===========111支付回调===')
    // 开始轮询支付状态
    await pollPaymentStatus(outTradeNo)
  } catch (error) {
    toast.close()
  }
}

/**
 * 轮询支付状态
 * @param outTradeNo 订单号
 */
const pollPaymentStatus = async (outTradeNo: string) => {
  const maxAttempts = 60
  const interval = 2000
  let attempts = 0
  const checkStatus = async (): Promise<void> => {
    attempts++
    try {
      const { data } = await payQueryDealStatus({
        outTradeNo,
      })
      if (data) {
        clearPollTimer()
        toast.close()

        toast.show('支付成功')
        setTimeout(() => {
          uni.$emit('paySuccess')
        }, 1500)

        return
      }
      if (attempts < maxAttempts) {
        console.log('attempts')
      } else {
        clearPollTimer()
        toast.close()
        message
          .alert({
            title: '提示',
            msg: '支付状态确认超时，请到订单页面查看支付结果',
            closeOnClickModal: false,
            confirmButtonText: '知道了',
          })
          .then(() => {
            // TODO: 跳转到订单页面
          })
      }
    } catch (error) {
      if (attempts < maxAttempts) {
        pollTimer.value = setTimeout(() => checkStatus(), interval)
      } else {
        clearPollTimer()
        toast.close()
        toast.error({
          msg: '网络异常',
          duration: 2000,
        })
      }
    }
  }
  pollTimer.value = setTimeout(() => checkStatus(), 1000)
}
/**
 * 清除轮询定时器
 */
const clearPollTimer = () => {
  if (pollTimer.value) {
    clearTimeout(pollTimer.value)
    pollTimer.value = null
  }
}
</script>

<style lang="scss" scoped>
:deep(.wd-count-down) {
  font-size: 24rpx !important;
  color: #ff4545 !important;
}
</style>
