<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="地址中心"></CustomNavBar>
    </template>
    <view class="px-40rpx py-40rpx">
      <view
        class="px-40rpx py-20rpx bg-#fff rounded-[20rpx] flex justify-between items-center"
        style="box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1)"
      >
        <wd-textarea
          v-model="adreesName"
          auto-height
          confirm-type="send"
          custom-class="w-100"
          no-border
          placeholder="定位公司地点"
          readonly
          @confirm="confirm"
        />
        <view @click="geographicToAddressList">
          <view class="w-88rpx h-54rpx lh-54rpx text-center bg-#f0f0f0 rounded-[10rpx]">
            <wd-img :height="13" :src="position" :width="13" />
          </view>
        </view>
      </view>
      <view v-if="imgMap" class="m-t-40rpx" style="box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1)">
        <image :src="imgMap" class="w-100 h-320rpx"></image>
      </view>
      <view
        class="m-t-40rpx px-60rpx py-20rpx bg-#fff rounded-[20rpx]"
        style="box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1)"
      >
        <wd-textarea
          v-model="fromData.address"
          auto-height
          custom-class="w-100"
          no-border
          placeholder="公司详细地址填写(必填)"
        />
      </view>
      <view
        class="m-t-40rpx px-60rpx py-20rpx bg-#fff rounded-[20rpx]"
        style="box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1)"
      >
        <wd-textarea
          v-model="fromData.remark"
          auto-height
          custom-class="w-100"
          no-border
          placeholder="备注信息(选填)"
        />
      </view>
      <view
        class="m-t-40rpx px-40rpx py-20rpx bg-#fff rounded-[20rpx]"
        style="box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1)"
      >
        <view class="flex items-center m-b-30rpx">
          <view class="text-28rpx c-#000 m-r-5rpx">拍摄公司照片</view>
          <wd-icon
            color="#888"
            name="info-circle-filled"
            size="15px"
            @click.stop="showDiog = true"
          ></wd-icon>
        </view>

        <view class="flex items-center w justify-between gap-20rpx m-b-40rpx">
          <wd-upload
            v-model:file-list="fileList2"
            :action="baseUrl"
            :before-upload="beforeUpload2"
            :header="header"
            :limit="1"
            :sourceType="sourceType"
            accept="image"
            custom-class="w-33 bg-#D9D9D9 h-200rpx flex items-center justify-center rounded-[10rpx] "
            image-mode="aspectFill"
            reupload
            @success="successFun2"
          >
            <view class="w-100 text-center rounded-[10rpx] m-auto">
              <wd-img :height="20" :src="upload" :width="20"></wd-img>
              <view class="text-22rpx c-#000 m-t-10rpx">门头照片</view>
            </view>
          </wd-upload>
          <wd-upload
            v-model:file-list="fileList3"
            :action="baseUrl"
            :before-upload="beforeUpload3"
            :header="header"
            :limit="1"
            :sourceType="sourceType"
            accept="image"
            custom-class="w-33 bg-#D9D9D9 h-200rpx flex items-center justify-center rounded-[10rpx] "
            image-mode="aspectFill"
            reupload
            @success="successFun3"
          >
            <view class="w-100 text-center rounded-[10rpx] m-auto">
              <wd-img :height="20" :src="upload" :width="20"></wd-img>
              <view class="text-22rpx c-#000 m-t-10rpx">招聘人员核验</view>
            </view>
          </wd-upload>
          <wd-upload
            v-model:file-list="fileList1"
            :action="baseUrl"
            :before-upload="beforeUpload1"
            :header="header"
            :limit="1"
            :sourceType="sourceType"
            accept="image"
            custom-class="w-33 bg-#D9D9D9 h-200rpx flex items-center justify-center rounded-[10rpx] "
            image-mode="aspectFill"
            reupload
            @success="successFun1"
          >
            <view class="w-100 text-center rounded-[10rpx] m-auto">
              <wd-img :height="20" :src="upload" :width="20"></wd-img>
              <view class="text-22rpx c-#000 m-t-10rpx">办公环境</view>
            </view>
          </wd-upload>
        </view>
        <!--        <view class="flex items-center w">-->
        <!--          -->
        <!--          <view class="w-45 m-r-40rpx"></view>-->
        <!--        </view>-->
        <view class="text-24rpx c-#666 m-t-20rpx">
          <view class="text-24rpx c-#666">注意:</view>
          <view class="text-24rpx c-#666">1.请先开启手机定位</view>
          <view class="text-24rpx c-#666">2.门头照片必须清晰，含有门牌号，公司名称标识等</view>
          <view class="text-24rpx c-#666">3.岗位发布人和公司名称标识合影照片</view>
          <view class="text-24rpx c-#666">4.办公环境照片需含有办公设备，工作人员等</view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="submit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
  <wd-popup v-model="showDiog" custom-style="height: 200px;" position="bottom">
    <view class="text-28rpx c-#000 m-t-40rpx m-l-20rpx">拍摄样例</view>
    <view
      style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 20rpx;
      "
    >
      <view v-for="(item, index) in imgList" :key="index" class="">
        <wd-img :enable-preview="true" :height="100" :src="item" :width="110" class="m-r-10rpx" />
        <view class="text-24rpx c-#000 text-center m-auto p-t-10rpx">
          {{ index === 0 ? '门头照片' : index === 1 ? '招聘人员核验' : '办公环境' }}
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { hrCompanyWorkAddressAdd } from '@/service/companyAdress'
import { geographicToAddress, addressToGeographic } from '@/interPost/common'
import { queryKey } from '@/interPost/home'
import position from '@/setting/img/position.png'
import upload from '@/sub_business/static/release/upload.png'
import company1 from '@/sub_business/static/company/company1.png'
import company2 from '@/sub_business/static/company/company2.png'
import company3 from '@/sub_business/static/company/company3.png'
import { quickHandlers } from '@/utils/compressImage'

const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// 弹窗
const showDiog = ref(false)
// 公司地址
const adreesName = ref('')
// 公司详细地址
const homeLocation = ref('')
const sourceType: any = ['camera']
// 参数
const fromData = ref({
  // 详细地址
  address: '',
  // 城市Code
  cityCode: '',
  // 城市名称
  cityName: '',
  // 区Code
  districtCode: '',
  //   区名称
  districtName: '',
  //   上传的图片id list
  imageIds: [],
  //   纬度
  lat: null,
  //   经度
  lon: null,
  //   省份Code ,
  provideCode: '',
  //   省份名称
  provideName: '',
  //   备注
  remark: '',
})
const fileList1 = ref([])
const fileList2 = ref([])
const fileList3 = ref([])
// 图片id
const imgId1 = ref('')
const imgId2 = ref('')
const imgId3 = ref('')

// 图片1上传
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// 弹窗图片
const imgList = ref([company3, company2, company1])
// before-upload 处理函数 - 使用预设的大尺寸压缩配置
const beforeUpload1 = quickHandlers.highQuality()
// before-upload 处理函数 - 使用预设的大尺寸压缩配置
const beforeUpload2 = quickHandlers.highQuality()
// before-upload 处理函数 - 使用预设的大尺寸压缩配置
const beforeUpload3 = quickHandlers.highQuality()
// 图片1上传成功
const successFun1 = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  if (res.code === 0) {
    imgId1.value = res.data[0].fileId
  }
}
// 图片2上传成功
const successFun2 = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  if (res.code === 0) {
    imgId2.value = res.data[0].fileId
  }
}
// 图片3上传成功
const successFun3 = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  if (res.code === 0) {
    imgId3.value = res.data[0].fileId
  }
}
// 天地图图片
const imgMap = ref('')
// 天地图key
const staticKey = ref('')

const submit = async () => {
  if (!fromData.value.lat || !fromData.value.lon) {
    uni.showToast({
      title: '请检查定位，定位失败',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!imgId1.value || !imgId2.value || !imgId3.value) {
    uni.showToast({
      title: '请上传公司相关图片',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.address) {
    uni.showToast({
      title: '请输入详细地址',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await hrCompanyWorkAddressAdd({
    ...fromData.value,
    imageIds: [imgId1.value, imgId2.value, imgId3.value],
  })
  if (res.code === 0) {
    uni.$emit('adressList')
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}

// 获取ket
const getMapKet = async () => {
  const res: any = await queryKey()
  if (res.code === 0) {
    staticKey.value = res.data.staticKey
  }
}
// 获取经纬度
const getLocationInfo = () => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'wgs84',
      success: function (res) {
        fromData.value.lat = res.latitude
        fromData.value.lon = res.longitude
        resolve(res)
      },
      fail: function (error) {
        uni.showToast({
          title: '获取定位失败',
          icon: 'none',
          duration: 3000,
        })
        reject(error)
      },
    })
  })
}
// 根据经纬度生成地址
const geographicToAddressList = async () => {
  // 如果没有经纬度，先获取位置

  try {
    await getLocationInfo()
  } catch (error) {
    console.error('获取位置失败:', error)
    return
  }

  // 确保有经纬度后再调用地址解析API
  if (!fromData.value.lat || !fromData.value.lon) {
    uni.showToast({
      title: '无法获取位置信息',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  const res: any = await geographicToAddress({
    lat: fromData.value.lat,
    lon: fromData.value.lon,
    ver: '31',
  })
  if (res.code === 0) {
    adreesName.value = res.data.formatted_address ? res.data.formatted_address : ''
    fromData.value.lat = res.data.location.lat
    fromData.value.lon = res.data.location.lon
    fromData.value.cityCode = res.data.addressComponent.city_code
      ? res.data.addressComponent.city_code
      : res.data.addressComponent.province_code
    fromData.value.cityName = res.data.addressComponent.city
      ? res.data.addressComponent.city
      : res.data.addressComponent.province
    fromData.value.districtCode = res.data.addressComponent.county_code
    fromData.value.districtName = res.data.addressComponent.county
    fromData.value.provideCode = res.data.addressComponent.province_code
    fromData.value.provideName = res.data.addressComponent.province
    if (fromData.value.lat && fromData.value.lon) {
      imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${fromData.value.lon},${fromData.value.lat}&width=300&height=200&zoom=12&tk=${staticKey.value}&markers=${fromData.value.lon},${fromData.value.lat}`
    }
  }
}
// d
const confirm = async () => {
  const res: any = await addressToGeographic({ address: adreesName.value })
  console.log(res, '地址换经纬度')
  if (res.code === 0) {
    adreesName.value = res.data.keyWord ? res.data.keyWord : ''

    fromData.value.lat = res.data.lat
    fromData.value.lon = res.data.lon
    if (fromData.value.lat && fromData.value.lon) {
      imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${fromData.value.lon},${fromData.value.lat}&width=300&height=200&zoom=12&tk=${staticKey.value}&markers=${fromData.value.lon},${fromData.value.lat}`
    }
  } else {
    uni.showToast({
      title: '获取定位失败',
      icon: 'none',
      duration: 3000,
    })
  }
}
onLoad(async (options) => {
  await getMapKet()
  // 页面加载时获取位置并解析地址
  await geographicToAddressList()
})
</script>

<style lang="scss" scoped>
:deep(.wd-upload__preview) {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;

  .btn_box {
    box-sizing: border-box;
    width: 100%;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
