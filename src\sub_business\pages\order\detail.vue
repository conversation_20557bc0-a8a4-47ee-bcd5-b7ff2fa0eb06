<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '详情',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" ref="pagingRef" layout-only>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">详情</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
      <view class="w-580rpx"></view>
    </template>
    <view class="px-40rpx p-b-40rpx p-t-20rpx">
      <view
        class="shadow-[0px_4px_13.5px_0px_rgba(0,0,0,0.15)] py-30rpx bg-white rounded-20rpx relative m-b-10rpx"
      >
        <view
          class="relative px-40rpx p-b-30rpx border-b-1px border-b-solid border-b-[#D8D8D8] m-b-20rpx"
        >
          <view class="flex items-center justify-between">
            <view class="text-36rpx font500 c-#333 p-b-6rpx">{{ pageData.goodsDesc }}</view>
            <wd-count-down
              v-if="!pageData.dealState && pageData.finishTimeD > 0"
              :time="pageData.finishTime"
              format="mm:ss"
            />
          </view>

          <view class="flex items-center gap-10rpx">
            <view class="text-28rpx c-#333">{{ pageData.positionName }}</view>
            <view class="text-32rpx c-#333">￥{{ pageData.actualAmount / 100 }}</view>
          </view>
          <view
            class="absolute top-30rpx right-0 p-r-40rpx text-32rpx c-#333"
            v-if="pageData.dealState"
          >
            {{ pageData.dealStateName }}
          </view>
          <view class="flex items-center" v-if="!pageData.dealState && pageData.finishTimeD <= 0">
            <view class="absolute top-30rpx right-40rpx c-#555 text-32rpx">支付过期</view>
          </view>
        </view>
        <view class="px-40rpx p-b-30rpx" v-if="pageData.bizOriginalPrice">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">价格</view>
            <view class="text-32rpx c-#333">￥{{ pageData.bizOriginalPrice / 100 }}元</view>
          </view>
        </view>
        <view class="px-40rpx p-b-30rpx" v-if="pageData.discountAmount">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">优惠</view>
            <view class="text-32rpx c-#333">￥{{ pageData.discountAmount / 100 }}元</view>
          </view>
        </view>
        <view class="px-40rpx p-b-30rpx" v-if="pageData.actualAmount">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">付款金额</view>
            <view class="text-32rpx c-#333">￥{{ pageData.actualAmount / 100 }}元</view>
          </view>
        </view>

        <view class="px-40rpx p-t-30rpx border-t-1px border-t-solid border-t-[#D8D8D8] m-t-10rpx">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">订单编号</view>
            <view class="text-26rpx c-#333">{{ pageData.outTradeNo }}</view>
          </view>
        </view>
        <view class="px-40rpx p-t-30rpx" v-if="pageData.dealCreateTime">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">下单时间</view>
            <view class="text-26rpx c-#333">{{ pageData.dealCreateTime }}</view>
          </view>
        </view>
        <view class="px-40rpx p-t-30rpx" v-if="pageData.successTime">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">付款时间</view>
            <view class="text-26rpx c-#333">{{ pageData.successTime }}</view>
          </view>
        </view>
        <view class="px-40rpx p-t-30rpx" v-if="pageData.finishTime">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">完成时间</view>
            <view class="text-26rpx c-#333">{{ pageData.finishTime }}</view>
          </view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed" v-if="!pageData.dealState && pageData.finishTimeD > 0">
        <view class="btn_box">
          <view class="btn_bg" @click="handlePay(pageData.dealId, pageData.outTradeNo)">
            确认支付￥{{ pageData.actualAmount / 100 }}元
          </view>
        </view>
      </view>
      <view class="btn-fixed" v-if="!pageData.dealState && pageData.finishTimeD <= 0">
        <view class="btn_box">
          <view class="btn_bg" @click="handleCancel">删除订单</view>
        </view>
      </view>
    </template>
  </z-paging>
  <wd-message-box />
</template>

<script setup lang="ts">
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { payDealDetail, payDealStatus, cancelDealStatus } from '@/service/order'
import { getTimeDifference } from '@/utils/util'
import { DICT_IDS } from '@/enum/diction'
import { payQueryDealStatus } from '@/service/pay'

import { useToast, useMessage } from 'wot-design-uni'
const toast = useToast()
const { getDictData } = useDictionary()

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const list = ref({})
const message = useMessage()
const pollTimer = ref<number | null>(null)
const dealId = ref<number>(null)
const pageData = ref<any>({})
const dealState = ref<number>(null)
const queryList = async () => {
  const res: any = await payDealDetail({ dealId: dealId.value, dealState: dealState.value })
  if (res.code === 0) {
    res.data.dealStateName = list.value[res.data.dealState] || ''
    if (!res.data.dealState) {
      res.data.finishTimeD = getTimeDifference(res.data.finishTime)
    }
    pageData.value = res.data
  }
  console.log(res, 'res=============')
}
// 去支付
const handlePay = async (dealId: number, outTradeNo: string) => {
  const res: any = await payDealStatus({ dealRecordId: dealId })
  console.log(res)
  if (res.code === 0) {
    await handlePayment(res.data, outTradeNo)
  }
}

// 支付函数
const payConnetMethod = (orderStr: any) => {
  return new Promise((resolve, reject) => {
    console.log(orderStr, 'orderStr===========111===')
    let payType = 1
    if (orderStr?.orderStr) {
      payType = 2
    }
    uni.requestPayment({
      provider: payType === 1 ? 'wxpay' : 'alipay',
      orderInfo: payType === 1 ? orderStr : orderStr.orderStr,
      success(res) {
        resolve(res)
      },
      fail(e) {
        reject(e)
      },
    })
  })
}
const handlePayment = async (orderStr: any, outTradeNo: string) => {
  try {
    toast.loading({
      msg: '支付中...',
      loadingType: 'outline',
      duration: 0,
      cover: true,
    })
    const res = await payConnetMethod(orderStr)
    console.log(res, 'res===========111支付回调===')
    // 开始轮询支付状态
    await pollPaymentStatus(outTradeNo)
  } catch (error) {
    toast.close()
  }
}

/**
 * 轮询支付状态
 * @param outTradeNo 订单号
 */
const pollPaymentStatus = async (outTradeNo: string) => {
  const maxAttempts = 60
  const interval = 2000
  let attempts = 0
  const checkStatus = async (): Promise<void> => {
    attempts++
    try {
      const { data } = await payQueryDealStatus({
        outTradeNo,
      })
      if (data) {
        clearPollTimer()
        toast.close()
        toast.show('支付成功')
        setTimeout(() => {
          queryList()
        }, 1500)

        return
      }
      if (attempts < maxAttempts) {
        console.log('attempts')
      } else {
        clearPollTimer()
        toast.close()
        message
          .alert({
            title: '提示',
            msg: '支付状态确认超时，请到订单页面查看支付结果',
            closeOnClickModal: false,
            confirmButtonText: '知道了',
          })
          .then(() => {
            // TODO: 跳转到订单页面
          })
      }
    } catch (error) {
      if (attempts < maxAttempts) {
        pollTimer.value = setTimeout(() => checkStatus(), interval)
      } else {
        clearPollTimer()
        toast.close()
        toast.error({
          msg: '网络异常',
          duration: 2000,
        })
      }
    }
  }
  pollTimer.value = setTimeout(() => checkStatus(), 1000)
}
/**
 * 清除轮询定时器
 */
const clearPollTimer = () => {
  if (pollTimer.value) {
    clearTimeout(pollTimer.value)
    pollTimer.value = null
  }
}

function handleClickLeft() {
  uni.navigateBack()
}
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
// 取消订单
const handleCancel = async () => {
  message
    .confirm({
      title: '提示',
      msg: '您确定删除订单吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(async () => {
      const res: any = await cancelDealStatus({ dealId: pageData.value.dealId })
      if (res.code === 0) {
        toast.show('删除成功')
        queryList()
      }
    })
}
// 订单字典
const getDictDataType = async () => {
  const res: any = await getDictData(DICT_IDS.ORDER_PAY_TYPE)
  list.value = res
  console.log(list.value)
}
onLoad(async (options) => {
  dealId.value = options.dealId
  dealState.value = options.dealState
  await getDictDataType()
  await queryList()
})
</script>

<style lang="scss" scoped>
.btn-fixed {
  padding: 10rpx 80rpx;
  margin-bottom: 60rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 0rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
:deep(.wd-count-down) {
  font-size: 34rpx !important;
  color: #ff4545 !important;
}
</style>
