<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '快速处理',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle" safe-area-inset-bottom>
    <template #top>
      <wd-navbar
        :bordered="false"
        safe-area-inset-top
        placeholder
        custom-class="!bg-transparent px-30rpx"
        left-arrow
        @click-left="handleClickLeft"
      >
        <template #title>
          <text class="c-#333333 font500 text-32rpx">快速处理</text>
        </template>
      </wd-navbar>
    </template>
    <view class="flex flex-col px-50rpx mt-100rpx">
      <view class="bg-#45A2FF rounded-20rpx p30rpx mb-110rpx relative" v-if="cards[0]?.msg">
        <text class="c-#FFFFFF font500 text-24rpx line-clamp-2 whitespace-pre-wrap">
          {{ cards[0].msg }}
        </text>
        <view class="absolute bottom--40rpx h-40rpx w-92rpx left-86rpx">
          <wd-img :src="msgIcon" width="100%" height="100%" />
        </view>
      </view>
      <view class="relative h-878rpx w-full flex justify-center">
        <view
          v-for="(item, key) in visibleCards"
          :key="`card-${sm4Decrypt(item.trueName)}${item.gender}-${key}`"
          :class="getCardClass(key)"
          :style="{
            ...getCardStyle(key),
            transition: isProcessing
              ? 'none'
              : 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            willChange: 'transform',
          }"
          @touchstart="onTouchStart"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
          class="absolute w-full h-full flex flex-col p-[58rpx_32rpx] bg-white rounded-20rpx shadow-lg border border-gray-100 card-optimized"
        >
          <view class="flex flex-col items-center mt--110rpx">
            <wd-img
              :src="findConversationByUserId(item.cUserId).avatar"
              round
              height="110rpx"
              width="110rpx"
            />
            <text class="c-#333333 font500 text-36rpx mt-12rpx">{{ item?.trueName }}</text>
            <text class="c-#666666 text-26rpx">
              {{
                [
                  item?.positionName,
                  formatSalary(item?.salaryExpectationStart, item?.salaryExpectationEnd),
                ]
                  .filter(Boolean)
                  .join('·')
              }}
            </text>
            <text class="c-#333333 text-26rpx">
              {{ [item?.age, item?.workYear, item?.seekStatusLabel].filter(Boolean).join('·') }}
            </text>
          </view>
          <view class="flex-1 overflow-y-auto">
            <view
              class="py-30rpx px-14rpx flex items-center border-b-1px border-b-solid border-b-[#DBDBDB]"
            >
              <text class="flex-1 line-clamp-1 c-#333333 text-28rpx font500">
                {{ item?.school ?? '教育经历' }}
              </text>
              <text class="c-#888888 text-28rpx">
                {{
                  [item?.qualificationLabel, item?.major].filter(Boolean).join('·') ||
                  '未填写教育经历'
                }}
              </text>
            </view>
            <view
              class="py-30rpx px-14rpx flex flex-col gap-20rpx border-b-1px border-b-solid border-b-[#DBDBDB]"
            >
              <text class="flex-1 line-clamp-1 c-#333333 text-28rpx font500">个人优势</text>
              <text class="whitespace-pre-wrap c-#333333 text-24rpx">{{ item?.myLights }}</text>
            </view>
            <view class="py-30rpx px-14rpx flex flex-col gap-6rpx">
              <text class="flex-1 line-clamp-1 c-#333333 text-28rpx font500">工作经历</text>
              <template v-if="!item?.workExperiencesList?.length">
                <view
                  class="py-30rpx px-14rpx flex items-center border-b-1px border-b-solid border-b-[#DBDBDB]"
                >
                  <text class="flex-1 line-clamp-1 c-#333333 text-24rpx font500">
                    未填写工作经历
                  </text>
                  <text class="c-#888888 text-24rpx">无</text>
                </view>
              </template>
              <template v-else>
                <view
                  class="py-30rpx px-14rpx flex items-center border-b-1px border-b-solid border-b-[#DBDBDB]"
                  v-for="(res, val) in item.workExperiencesList"
                  :key="`work-${sm4Decrypt(res.company)}-${val}`"
                >
                  <text class="flex-1 line-clamp-1 c-#333333 text-24rpx font500">
                    {{ res.company }}
                  </text>
                  <text class="c-##333333 text-24rpx">
                    {{
                      [res?.positionName, item?.workYear ? `${item.workYear}年` : '']
                        .filter(Boolean)
                        .join('·')
                    }}
                  </text>
                </view>
              </template>
            </view>
          </view>
        </view>
        <view v-if="visibleCards.length === 0" class="flex flex-col items-center mt-250rpx">
          <wd-img :src="releasePostEmpty" width="412rpx" height="412rpx" />
          <text class="c-#333333 text-28rpx">暂无新招呼</text>
        </view>
      </view>
    </view>
    <template #bottom v-if="visibleCards.length">
      <view class="flex items-center gap-30rpx px-52rpx py-38rpx">
        <wd-button
          custom-class="!bg-#666666 w-full !h-106rpx"
          size="large"
          :round="false"
          @click="handleRejectClick"
          :disabled="isProcessing"
        >
          <text class="c-#FFFFFF text-30rpx font500">不合适</text>
        </wd-button>
        <wd-button
          custom-class="w-full !h-106rpx !bg-gradient-to-l from-[#DDDCFF] to-[#FFC2C2]"
          size="large"
          :round="false"
          @click="handleAcceptClick"
          :disabled="isProcessing"
        >
          <text class="c-#333333 text-30rpx font500">打招呼</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { formatSalary } from '@/utils'
import { CONVERSATION_MARKS, DICT_IDS } from '@/enum'
import { hrUnInterestUserAdd } from '@/service/hrUnInterestUser'
import { hrResumeQuickCardInfo } from '@/service/hrResume'
import type { Chat } from '@/ChatUIKit/types'
import type { hrResumeQuickCardInfoInt } from '@/service/hrResume/types'
import type { ConversationWithUserInfo } from '@/hooks/common/useIMConversation'
import releasePostEmpty from '@/static/home/<USER>/release-post-empty.png'
import msgIcon from '@/sub_business/static/rapid-processing/msg-icon.png'

interface CardsInt extends hrResumeQuickCardInfoInt {
  msg?: string
}

const { sm4Decrypt } = useSmCrypto({
  type: 'password',
})
const { sysScreenWidth } = useSystemInfo()
const { getConversationsByMark, sendGreetingMessage, deleteConversation, getIMLoginId } =
  useIMConversation()
const { getDictLabel } = useDictionary()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const conversationsNewGreeting = ref<ConversationWithUserInfo[]>([])
const cards = ref<CardsInt[]>([])
const conversationIndex = ref(0)
const isLoading = ref(false)
const hasMoreData = ref(true)
const maxCacheSize = 5
const minCacheThreshold = 2

const touchState = reactive({
  startX: 0,
  startY: 0,
  currentX: 0,
  currentY: 0,
  isDragging: false,
})

const swipeDirection = ref<'left' | 'right' | null>(null)
const cardOffset = ref(0)
const cardRotation = ref(0)
const isProcessing = ref(false)
let lastTouchTime = 0

const ANIMATION_CONFIG = {
  ROTATION_ANGLE: 15,
  THROTTLE_DELAY: 8,
  SWIPE_THRESHOLD: 100,
  MIN_MOVE_THRESHOLD: 20,
  DIRECTION_THRESHOLD: 30,
  OPACITY_FACTOR: 0.8,
  ANIMATION_DELAY: 300,
  PROCESSING_DELAY: 50,
}
const resetAnimationState = () => {
  cardOffset.value = 0
  cardRotation.value = 0
  swipeDirection.value = null
}

const executeCardAnimation = (direction: 'left' | 'right') => {
  const multiplier = direction === 'right' ? 1 : -1
  swipeDirection.value = direction
  cardOffset.value = multiplier * sysScreenWidth.value
  cardRotation.value = multiplier * ANIMATION_CONFIG.ROTATION_ANGLE
}

const throttledTouchMove = (deltaX: number, deltaY: number) => {
  const now = Date.now()
  if (now - lastTouchTime < ANIMATION_CONFIG.THROTTLE_DELAY) return
  lastTouchTime = now
  if (Math.abs(deltaX) > ANIMATION_CONFIG.MIN_MOVE_THRESHOLD) {
    cardOffset.value = deltaX
    cardRotation.value = (deltaX / sysScreenWidth.value) * ANIMATION_CONFIG.ROTATION_ANGLE
    if (Math.abs(deltaX) > ANIMATION_CONFIG.DIRECTION_THRESHOLD) {
      swipeDirection.value = deltaX > 0 ? 'right' : 'left'
    } else {
      swipeDirection.value = null
    }
  }
}
const visibleCards = computed(() => {
  return cards.value.slice(0, 3)
})
const getCardClass = (index: number) => {
  if (index === 0) {
    return 'z-30'
  } else if (index === 1) {
    return 'z-20'
  } else {
    return 'z-10'
  }
}
const getCardStyle = (index: number) => {
  if (index !== 0) {
    return { transform: 'translate3d(0, 0, 0) scale(1)' }
  }
  const opacity = Math.max(
    0.3,
    1 - Math.abs(cardOffset.value) / (sysScreenWidth.value * ANIMATION_CONFIG.OPACITY_FACTOR),
  )
  return {
    transform: `translate3d(${cardOffset.value}px, 0, 0) scale(1) rotate(${cardRotation.value}deg)`,
    opacity,
  }
}
const onTouchStart = (e: TouchEvent) => {
  if (isProcessing.value) return
  touchState.startX = e.touches[0].clientX
  touchState.startY = e.touches[0].clientY
  touchState.currentX = touchState.startX
  touchState.currentY = touchState.startY
  touchState.isDragging = true
}
const onTouchMove = (e: TouchEvent) => {
  if (!touchState.isDragging || isProcessing.value) return
  const currentX = e.touches[0].clientX
  const currentY = e.touches[0].clientY
  const deltaX = currentX - touchState.startX
  const deltaY = Math.abs(currentY - touchState.startY)
  touchState.currentX = currentX
  touchState.currentY = currentY
  throttledTouchMove(deltaX, deltaY)
}
const onTouchEnd = () => {
  if (!touchState.isDragging || isProcessing.value) return
  const deltaX = touchState.currentX - touchState.startX
  touchState.isDragging = false
  if (Math.abs(deltaX) > ANIMATION_CONFIG.SWIPE_THRESHOLD) {
    isProcessing.value = true
    const direction = deltaX > 0 ? 1 : -1
    cardOffset.value = direction * sysScreenWidth.value
    cardRotation.value = direction * ANIMATION_CONFIG.ROTATION_ANGLE
    if (deltaX > 0) {
      acceptCard()
    } else {
      rejectCard()
    }
  } else {
    resetAnimationState()
  }
}

const handleButtonClick = async (action: 'accept' | 'reject') => {
  if (isProcessing.value || visibleCards.value.length === 0) return
  const direction = action === 'accept' ? 'right' : 'left'
  executeCardAnimation(direction)
  setTimeout(() => {
    isProcessing.value = true
    action === 'accept' ? acceptCard() : rejectCard()
  }, ANIMATION_CONFIG.ANIMATION_DELAY)
}

const handleRejectClick = () => handleButtonClick('reject')
const handleAcceptClick = () => handleButtonClick('accept')

const fetchResumeInfo = async (
  userId: number,
  retryCount = 1,
): Promise<hrResumeQuickCardInfoInt | null> => {
  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      const response = await hrResumeQuickCardInfo(
        { userId },
        {
          custom: {
            catch: true,
            toast: true,
          },
        },
      )
      return response.data || null
    } catch (error) {
      if (attempt === retryCount) {
        console.warn(`获取简历信息失败 (userId: ${userId}):`, error)
        return null
      }
      await new Promise((resolve) => setTimeout(resolve, 200 * (attempt + 1)))
    }
  }
  return null
}

const preloadCards = async (targetCount: number = 3) => {
  if (isLoading.value || !hasMoreData.value) return

  isLoading.value = true
  const loadPromises: Promise<void>[] = []

  try {
    while (
      loadPromises.length < targetCount &&
      conversationIndex.value < conversationsNewGreeting.value.length &&
      cards.value.length < maxCacheSize
    ) {
      const conversation = conversationsNewGreeting.value[conversationIndex.value]
      await uni.$UIKit.messageStore.getHistoryMessages({
        conversationId: conversation.conversationId,
        conversationType: conversation.conversationType,
      } as Chat.ConversationItem)
      const userExt = getUserExt(conversation.ext)
      conversationIndex.value++

      if (userExt.cUserId <= 0) continue

      loadPromises.push(
        fetchResumeInfo(userExt.cUserId).then(async (resumeInfo) => {
          if (resumeInfo) {
            const [qualificationLabel, seekStatusLabel] = await Promise.all([
              getQualificationLabel(resumeInfo?.qualification),
              getSeekStatusLabel(resumeInfo?.seekStatus),
            ])
            const msgList = uni.$UIKit.messageStore.conversationMessagesMap
              .get(conversation.conversationId)
              .messageIds.map((id) => {
                return uni.$UIKit.messageStore.messageMap.get(id)
              })
            const newMsg = msgList.find((msg) => msg?.from === getIMLoginId.value)
            cards.value.push({
              ...resumeInfo,
              cUserId: userExt.cUserId,
              qualificationLabel,
              seekStatusLabel,
              msg: (newMsg as AnyObject)?.msg || '',
            })
          }
        }),
      )
    }
    await Promise.all(loadPromises)
    if (conversationIndex.value >= conversationsNewGreeting.value.length) {
      hasMoreData.value = false
    }
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}

const smartLoadCards = async () => {
  if (cards.value.length <= minCacheThreshold && hasMoreData.value && !isLoading.value) {
    const needCount = maxCacheSize - cards.value.length
    await preloadCards(needCount)
  }
}

const initializeCards = async () => {
  if (conversationsNewGreeting.value.length === 0) {
    hasMoreData.value = false
    return
  }
  cards.value = []
  conversationIndex.value = 0
  hasMoreData.value = true

  await preloadCards(maxCacheSize)

  if (cards.value.length < 3 && hasMoreData.value) {
    await preloadCards(3 - cards.value.length)
  }
}

const processCard = async (action: 'accept' | 'reject') => {
  if (visibleCards.value.length === 0) return
  const currentCard = cards.value[0]
  const userId = currentCard.cUserId
  const conversation = findConversationByUserId(userId)
  if (!conversation) {
    console.warn('未找到对应的会话信息')
    return
  }
  console.log(`${action === 'accept' ? '快速回复' : '拒绝'}:`, currentCard?.trueName || '未知简历')
  try {
    if (action === 'accept') {
      await sendGreetingMessage(
        conversation.conversationId,
        {
          positionName: currentCard.positionName,
          hxUserInfoVO: { userId },
        },
        false,
        true,
      )
    } else {
      await Promise.all([
        hrUnInterestUserAdd({ userId }),
        deleteConversation(conversation.conversationId),
      ])
    }
    cards.value.shift()
    resetAnimationState()
    await smartLoadCards()
  } finally {
    await nextTick()
    setTimeout(() => {
      isProcessing.value = false
    }, ANIMATION_CONFIG.PROCESSING_DELAY)
  }
}

const rejectCard = async () => {
  await processCard('reject')
}

const acceptCard = async () => {
  await processCard('accept')
}

function getUserExt(ext: string): Api.IM.UserBusinessExtInfo {
  try {
    if (ext) {
      const parsed = JSON.parse(ext)
      return {
        cUserId: parsed.cUserId || 0,
        ...parsed,
      }
    }
  } catch (error) {
    console.warn('解析用户扩展信息失败:', error)
  }
  return {
    cUserId: 0,
  }
}
const findConversationByUserId = (userId: number) => {
  return (
    conversationsNewGreeting.value.find((conversation) => {
      const userExt = getUserExt(conversation.ext)
      return userExt.cUserId === userId
    }) || { conversationId: '', avatar: '' }
  )
}

async function getQualificationLabel(qualification: number) {
  return (await getDictLabel(DICT_IDS.EDUCATION_REQUIREMENT, qualification)) as string
}
async function getSeekStatusLabel(seekStatus: number) {
  return (await getDictLabel(DICT_IDS.SEEK_STATUS, seekStatus)) as string
}
const handleClickLeft = () => {
  uni.navigateBack()
}
async function getConversationsNewGreeting() {
  const conversations = await getConversationsByMark(CONVERSATION_MARKS.ONLY_CHAT)
  conversationsNewGreeting.value = conversations
}
onMounted(async () => {
  await uni.$onLaunched
  await getConversationsNewGreeting()
  await initializeCards()
})

onUnmounted(() => {
  isProcessing.value = false
  touchState.isDragging = false
  resetAnimationState()
})
</script>

<style lang="scss" scoped>
.card-optimized {
  transition: transform 0.3s ease-out;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
