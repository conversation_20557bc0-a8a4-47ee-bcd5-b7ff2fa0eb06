<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '招聘数据',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">招聘数据</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="flex flex-col gap-16rpx px-60rpx mt-44rpx">
      <view class="flex flex-col gap-32rpx">
        <!-- 顶部 -->
        <view class="m-b-32rpx">
          <wd-tabs v-model="tab" custom-class="custom-class" swipeable @change="handleTabChange">
            <block v-for="item in tabList" :key="item.value">
              <wd-tab :title="item.name"></wd-tab>
            </block>
          </wd-tabs>
        </view>
        <!-- 图表 -->
        <view
          class="w-100 h-[400rpx] bg-#2E6CFA rounded-20rpx p-r-80rpx p-l-0rpx p-t-20rpx p-b-20rpx"
        >
          <qiun-data-charts v-if="isChartReady" :chartData="chartData" :opts="opts" type="line" />
        </view>
        <!-- 数据总览 -->
        <view class="data-overview-box">
          <!-- 突起的白色背景部分 -->
          <view class="overview-header">
            <view class="header-content">
              <image class="header-image" src="/static/mine/business/up_icon.png"></image>
            </view>
          </view>
          <!-- 主要内容区域 -->
          <view class="overview-content">
            <view v-for="item in dataList" :key="item.id" class="data-item">
              <view class="data-label">{{ item.name }}</view>
              <view class="data-row">
                <view
                  :class="
                    item.value === 0
                      ? 'positive'
                      : item.type === 'increase'
                        ? 'positive'
                        : 'negative'
                  "
                  class="data-value"
                >
                  <!--                  {{ item.type === 'increase' ? `+ ${item.value}` : `- ${item.value}` }}-->
                  {{ handleUnit(item) }}
                  <text v-if="item?.value >= 0" class="text-22rpx font400 c-#333333">人</text>
                </view>
                <view class="data-icon">
                  <image
                    v-if="item.type === 'increase' && item.value > 0"
                    class="rise-icon"
                    src="/static/mine/business/rise_icon.png"
                  />
                  <image
                    v-if="item.type === 'reduce' && item.value < 0"
                    class="reduce-icon"
                    src="/static/mine/business/drop_icon.png"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { recruitmentData, recruitmentDataChart } from '@/service/recruitmentData'

// 日期格式化函数
const formatDate = (date: Date): string => {
  const y = date.getFullYear()
  let m: any = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d: any = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d
}

// 格式化日期时间 - 开始时间 (00:00:00)
const formatStartDateTime = (date: Date): string => {
  const y = date.getFullYear()
  let m: any = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d: any = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d + ' 00:00:00'
}

// 格式化日期时间 - 结束时间 (23:59:59)
const formatEndDateTime = (date: Date): string => {
  const y = date.getFullYear()
  let m: any = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d: any = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d + ' 23:59:59'
}

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

const currentTime = ref<string>('')
const pastTime = ref<any>('')
const chartData = ref<any>({
  categories: [],
  series: [
    {
      name: '数据',
      data: [],
    },
  ],
})
const isChartReady = ref(false)
const tab = ref(0)
const tabList = ref([
  {
    name: '已沟通',
    value: 1,
  },
  {
    name: '有交换',
    value: 2,
  },
  {
    name: '看过我',
    value: 3,
  },
  {
    name: '已约面',
    value: 4,
  },
])

const dataList = ref([
  {
    id: 1,
    name: '已沟通',
    value: 0,
    type: 'increase',
  },
  {
    id: 2,
    name: '有交换',
    value: 0,
    type: 'increase',
  },
  {
    id: 3,
    name: '看过我',
    value: 0,
    type: 'increase',
  },
  {
    id: 4,
    name: '已约面',
    value: 0,
    type: 'increase',
  },
])
const opts = ref({
  color: [
    '#ffffff',
    '#dashed',
    '#FAC858',
    '#EE6666',
    '#73C0DE',
    '#3CA272',
    '#FC8452',
    '#9A60B4',
    '#ea7ccc',
  ],
  padding: [10, 10, 0, 10],
  enableScroll: false,
  legend: {
    show: false,
  },
  xAxis: {
    show: true,
    axisLine: true,
    disableGrid: false,
    gridType: 'dash',
    title: '',
    fontSize: 10, // 字体大小
    fontColor: '#ffffff',
    rotateLabel: false, // 旋转标签
    itemCount: 7, // 显示的标签数量
    margin: 8, // 边距
  },
  yAxis: {
    show: true, // 显示y轴
    disableGrid: false,
    gridType: 'dash',
    dashLength: 2,
    fontSize: 10,
    fontColor: '#ffffff',
    data: [
      {
        fontSize: 10,
        fontColor: '#ffffff',
        axisLine: true,
      },
    ],
  },
  extra: {
    line: {
      type: 'curve',
      width: 2,
      activeType: 'hollow',
    },
  },
})

function handleClickLeft() {
  uni.navigateBack()
}

function handleTabChange() {
  isChartReady.value = false
  nextTick(() => {
    getServerData()
  })
}

const getServerData = async () => {
  const tabIndex = tab.value >= 0 && tab.value < tabList.value.length ? tab.value : 0
  const selectedTabValue = tabList.value[tabIndex]?.value
  const res: any = await recruitmentDataChart({
    type: selectedTabValue,
    startTime: pastTime.value, // 前7天 00:00:00
    endTime: currentTime.value, // 当前时间 23:59:59
  })
  if (res.code === 0) {
    // 生成前七天至当前日期的日期数组
    const categories: string[] = []
    const startDate = new Date(pastTime.value)
    const endDate = new Date(currentTime.value)

    // 修复：生成8天，从前7天到今天
    for (let i = 0; i <= 6; i++) {
      const date = new Date(startDate)
      date.setDate(startDate.getDate() + i)
      const formattedDate = formatDate(date)
      const dateWithoutYear = formattedDate.substring(5)
      categories.push(dateWithoutYear)
    }

    // 根据日期匹配数据，没有匹配的设为0
    const seriesData: number[] = []
    categories.forEach((date) => {
      const matchedData = res.data?.find((item: any) => {
        const serverDate = item.dates
        const serverDateWithoutYear = serverDate ? serverDate.substring(5) : ''
        return serverDateWithoutYear === date
      })
      seriesData.push(matchedData ? matchedData.counts : 0)
    })

    // 更新图表数据
    const resData = {
      categories,
      series: [
        {
          axisLine: false,
          textColor: 'transparent',
          pointShape: 'none',
          name: '',
          data: seriesData,
        },
      ],
    }
    chartData.value = JSON.parse(JSON.stringify(resData))
    isChartReady.value = true
  }
}

// 视图汇总
const getRecruitmentData = async () => {
  const res: any = await recruitmentData({
    startTime: pastTime.value, // 前7天 00:00:00
    endTime: currentTime.value, // 当前时间 23:59:59
  })
  if (res.code === 0) {
    dataList.value[0].value = res.data.imSession || 0
    dataList.value[1].value = res.data.exchangeTotal || 0
    dataList.value[2].value = res.data.seeMe || 0
    dataList.value[3].value = res.data.meetings || 0
  }
}

// 处理数据回显
const handleUnit = (item: any) => {
  if (item.value === 0) {
    return 0
  }
  if (item.type === 'increase') {
    return item.value
  } else {
    return item.value
  }
}

onMounted(async () => {
  await uni.$onLaunched
  const today = new Date()
  currentTime.value = formatEndDateTime(today) // 当前时间 23:59:59
  const pastDate = new Date()
  pastDate.setDate(today.getDate() - 6)
  pastTime.value = formatStartDateTime(pastDate) // 前7天 00:00:00

  await getServerData()
  await getRecruitmentData()
})
onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
:deep(.custom-class) {
  width: 100%;
  padding: 10rpx 5rpx;
  margin: 0 auto;
  background-color: transparent;
  border: 1rpx solid #545454;
  border-radius: 48rpx;

  .wd-tabs__nav {
    background-color: transparent;

    .wd-tabs__nav-item {
      //   height: 80%;
      font-size: 28rpx;
      font-weight: normal;
    }

    .is-active {
      font-size: 28rpx;
      font-weight: normal;
      color: #ffffff;
      background: #2e6cfa;
      border-radius: 48rpx;
    }

    .wd-tabs__line {
      background: transparent !important;
    }
  }
}

.data-overview-box {
  position: relative;
  margin-top: 40rpx;

  .overview-header {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: center;
    margin-bottom: -20rpx;

    .header-content {
      position: relative;
      background: #ffffff;
      border-radius: 20rpx 20rpx 0 0;

      .header-image {
        width: 100rpx;
        height: 20rpx;
        margin-bottom: 10rpx;
      }
      /* 创建更自然的突起效果 */
      &::before {
        position: absolute;
        top: -16rpx;
        left: 50%;
        width: 160rpx;
        height: 56rpx;
        content: '';
        background: #ffffff;
        border-top: 2rpx solid #f0f0f0;
        border-radius: 50% 50% 0 0;
        transform: translateX(-50%);
      }
    }
  }

  .overview-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;
    padding: 52rpx 30rpx 160rpx;
    background: #ffffff;
    border: 2rpx solid #f0f0f0;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .data-item {
      display: flex;
      flex-direction: column;
      padding: 20rpx;
      border: 4rpx dashed #b9b9b9;
      border-radius: 12rpx;

      .data-label {
        margin-bottom: 10rpx;
        font-size: 28rpx;
        font-weight: normal;
        line-height: 1.4;
        color: #000000;
      }

      .data-value {
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: 600;

        &.positive {
          color: #2e6cfa;
        }

        &.negative {
          color: #fb4f4f;
        }
      }

      .data-row {
        display: flex;
        gap: 8rpx;
        align-items: center;
        justify-content: space-between;
      }

      .data-icon {
        width: 36rpx;
        height: 36rpx;
        font-size: 24rpx;

        .rise-icon {
          width: 100%;
          height: 100%;
        }

        .reduce-icon {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
