<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-navbar
        :bordered="false"
        safe-area-inset-top
        placeholder
        custom-class="!bg-transparent"
        @click-left="handleNavLeft"
      >
        <template #left>
          <wd-icon name="arrow-left" size="40rpx" color="#333333" />
        </template>
        <template #title>
          <text class="c-#333333 text-32rpx font-500">{{ stepsInfo.title }}</text>
        </template>
      </wd-navbar>
    </template>
    <view class="component-wrapper" :class="{ 'is-transitioning': isTransitioning }">
      <view :class="['component-content', animationClass]">
        <component
          :is="stepsInfo?.component"
          v-bind="stepsInfo?.props ?? {}"
          :key="releaseCurrentStep"
          ref="currentStepRef"
          @next-step="goToStep"
        />
      </view>
    </view>

    <template #bottom>
      <view class="px-60rpx m-t-20rpx">
        <text class="c-#888888 text-22rpx font-400">
          发布岗位即表示同意
          <text class="c-#4128FF" @click="handleAgreement">《 招聘行为管理规范 》</text>
          ，如违反规则将可能导致您的账号使用受限制
        </text>
      </view>
      <view class="center flex-col m-b-20rpx m-t-20rpx">
        <view class="center flex-col pb-40rpx gap-30rpx">
          <wd-button
            :round="false"
            custom-class="w-590rpx !h-100rpx !bg-transparent !bg-gradient-to-r from-[#FFC2C2] to-[#DDDCFF] !rounded-28rpx"
            @click="handleActions"
          >
            <text class="c-#333333 text-28rpx font-500">{{ stepsInfo?.butText ?? '完成' }}</text>
          </wd-button>
          <wd-button
            v-if="stepsInfo.step === ReleaseStep.RELEASE"
            :round="false"
            custom-class="w-590rpx !h-100rpx !bg-#D9D9D9 !rounded-28rpx"
            @click="handleSaveDraft"
          >
            <text class="c-#333333 text-28rpx font-500">保存草稿</text>
          </wd-button>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import type { Component } from 'vue'
import { EMIT_EVENT } from '@/enum'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { ReleaseStep, ReleasePositionInfo } from '@/sub_business/types/release'
import { hrPositionAdd } from '@/service/hrPosition'
import release from './module/release.vue'
import position from './module/position.vue'
import describe from './module/describe.vue'

interface StepItem {
  step: ReleaseStep
  title: string
  component: Component
  butText?: string
  props?: Record<string, any>
  actions?: () => void
}

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { releaseSubmitPostModel, releaseCurrentStep, resetReleasePostModel } = useReleasePost()
const currentStepRef = ref()
const STEP_CONFIGS: StepItem[] = [
  {
    step: ReleaseStep.RELEASE,
    title: '发布岗位',
    butText: '付费发布',
    component: release,
    actions: CommonUtil.debounce(async () => {
      try {
        if (currentStepRef.value && typeof currentStepRef.value.releaseValidate === 'function') {
          const { valid } = await currentStepRef.value.releaseValidate()
          if (
            releaseSubmitPostModel.value.salaryType === 1 &&
            (!releaseSubmitPostModel.value.salaryFixed ||
              !releaseSubmitPostModel.value.salaryFloat ||
              !releaseSubmitPostModel.value.salaryMonths)
          ) {
            uni.showToast({
              title: '请先设置薪资结构',
              icon: 'none',
            })
            return
          }
          if (valid) {
            try {
              const { data } = await hrPositionAdd(releaseSubmitPostModel.value, {
                custom: {
                  catch: true,
                  loading: true,
                },
              })
              uni.$emit(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
              // resetReleasePostModel()
              uni.navigateTo({
                url: CommonUtil.buildUrlWithParams('/sub_business/pages/prop/pay', {
                  positionId: `${data}`,
                }),
              })
            } catch (error) {
              uni.hideLoading()
              uni.showToast({
                title: error?.msg || '发布失败',
                icon: 'none',
                mask: true,
              })
            }
          }
        }
      } catch (error) {}
    }, 300),
  },
  {
    step: ReleaseStep.SALARY,
    title: '岗位待遇',
    component: position,
    butText: '完成',
    props: {
      type: ReleasePositionInfo.SALARY,
    },
    actions: () => {
      currentStepRef.value?.submitData?.()
      handleNavLeft()
    },
  },
  {
    step: ReleaseStep.KEYWORDS,
    title: '岗位关键词',
    component: position,
    butText: '完成',
    props: {
      type: ReleasePositionInfo.KEYWORDS,
    },
    actions: () => {
      currentStepRef.value?.submitData?.()
      handleNavLeft()
    },
  },
  {
    step: ReleaseStep.DESCRIBE,
    title: '岗位描述',
    component: describe,
    butText: '完成',
    actions: CommonUtil.debounce(async () => {
      await currentStepRef.value?.submitData?.()
      handleNavLeft()
    }, 300),
  },
]
const stepHistory = ref<number[]>([])
const isTransitioning = ref(false)
const animationClass = ref('')

const stepsInfo = computed(() => {
  const stepConfig = STEP_CONFIGS.find((item) => item.step === releaseCurrentStep.value)
  return stepConfig
})
function handleActions() {
  const stepConfig = stepsInfo.value
  stepConfig?.actions()
}
async function handleSaveDraft() {
  uni.showLoading({
    title: '保存中...',
    mask: true,
  })
  await hrPositionAdd(releaseSubmitPostModel.value)
  uni.$emit(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
  uni.hideLoading()
  uni.showToast({
    title: '草稿已保存',
    icon: 'none',
    duration: 1500,
    mask: true,
  })
  setTimeout(() => {
    uni.navigateBack()
    resetReleasePostModel()
  }, 1500)
}
function handleAgreement() {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/Recruitmentbehavior',
  })
}

function goToStep(step: number) {
  stepHistory.value.push(releaseCurrentStep.value)

  isTransitioning.value = true
  animationClass.value = 'slide-out'

  setTimeout(() => {
    releaseCurrentStep.value = step
    animationClass.value = 'slide-in'

    setTimeout(() => {
      isTransitioning.value = false
      animationClass.value = ''
    }, 300)
  }, 150)
}

function handleNavLeft() {
  if (releaseCurrentStep.value === ReleaseStep.RELEASE) {
    uni.navigateBack()
    return
  }

  const previousStep = stepHistory.value.pop() ?? ReleaseStep.RELEASE

  isTransitioning.value = true
  animationClass.value = 'slide-out-reverse'

  setTimeout(() => {
    releaseCurrentStep.value = previousStep
    animationClass.value = 'slide-in-reverse'

    setTimeout(() => {
      isTransitioning.value = false
      animationClass.value = ''
    }, 300)
  }, 150)
}
</script>

<style lang="scss" scoped>
.component-wrapper {
  position: relative;
  overflow: hidden;
}

.component-content {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  &.slide-in {
    animation: slideIn 0.3s ease-out forwards;
  }

  &.slide-out {
    animation: slideOut 0.15s ease-in forwards;
  }

  &.slide-in-reverse {
    animation: slideInReverse 0.3s ease-out forwards;
  }

  &.slide-out-reverse {
    animation: slideOutReverse 0.15s ease-in forwards;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-50rpx);
  }
}

@keyframes slideInReverse {
  from {
    opacity: 0;
    transform: translateX(-100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutReverse {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(50rpx);
  }
}
</style>
