<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging
    ref="pagingRef"
    :paging-style="pageStyle"
    bottom-bg-color="#f8f8f8"
    layout-only
    safe-area-inset-bottom
    @hidedKeyboard="hidedKeyboard"
    @keyboardHeightChange="keyboardHeightChange"
  >
    <template #top>
      <CustomNavBar title="客服小易"></CustomNavBar>
    </template>
    <view class="pageContaner relative">
      <view class="absolute top-10rpx">
        <wd-img :src="kf" height="122" width="112" />
      </view>
      <view class="absolute top-60rpx left-260rpx">
        <view class="c-000 text-36rpx font-fameliy">Hi～，有什么可以帮您！</view>
        <view class="c-#555 text-28rpx">智能小易为您服务</view>
      </view>
      <view class="p-t-200rpx">
        <view class="pageContaner-card flex items-center">
          <view
            v-for="(item, index) in listTopData"
            :key="index"
            class="w-25 text-center m-t-40rpx"
            @click="handleQuestionType(item.type)"
          >
            <wd-img
              :src="`/static/my/customerService/icon${item.icon}.png`"
              height="30"
              width="30"
            />
            <view class="c-#555 text-24rpx">{{ item.questionName }}</view>
          </view>
        </view>
      </view>
      <view class="m-t-40rpx">
        <view class="pageContaner-botto-card">
          <view class="flex items-center p-b-10rpx border-bottom m-b-10rpx">
            <wd-img :src="`/static/my/customerService/question.png`" height="22" width="22" />
            <view class="text-28rpx c-#333 p-l-6rpx font-400">猜您想问</view>
          </view>
          <view v-for="(item, index) in displayedQuestions" :key="index">
            <view
              class="c-#589BFF text-28rpx question-text py-16rpx"
              @click="handleQuestionClick(item)"
            >
              {{ item.question }}
            </view>
          </view>

          <!-- 查看更多/收起按钮 -->
          <view
            v-if="questionList.length > 5"
            class="flex items-center p-t-10rpx p-b-10rpx"
            @click="toggleQuestionDisplay"
          >
            <text class="text-24rpx c-#589bff">{{ isExpanded ? '收起' : '查看更多' }}</text>
            <view class="m-l-8rpx">
              <wd-icon
                :name="isExpanded ? 'arrow-up' : 'arrow-down'"
                color="#589bff"
                size="24rpx"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 聊天消息区域 -->
      <view v-if="chatMessages.length > 0" class="chat-messages-container">
        <view
          v-for="(message, index) in chatMessages"
          :id="`message-${index}`"
          :key="index"
          class="message-item"
        >
          <view :class="{ 'message-user': message.isUser }" class="message-container">
            <!-- <view class="message-avatar">
              <image
                class="avatar-img"
                :src="message.isUser ? '/static/my/customerService/user-avatar.png' : kf"
                mode="aspectFill"
              />
            </view> -->
            <view :class="{ 'bubble-user': message.isUser }" class="message-bubble">
              <text class="message-text">{{ message.content }}</text>
              <view
                v-if="!message.isUser && message.clickCount && message.clickCount >= 6"
                class="transfer-human-btn"
                @click="transferToHuman"
              >
                <text class="transfer-text">转人工-人工客服</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 底部锚点 -->
        <view id="chat-bottom" class="chat-bottom-anchor"></view>
      </view>
    </view>
    <!-- <template #bottom>
      <view :style="{ paddingBottom: keyboardHeight + 'px' }">
        <chat-input-bar :disabled="isAnswering" ref="inputBar" @send="sendMessage" />
      </view>
    </template> -->
  </z-paging>

  <!-- 查看更多问题弹窗 -->
  <!-- <wd-popup v-model="showQuestionPopup" position="bottom" :safe-area-inset-bottom="true">
    <view class="question-popup">
      <view class="popup-header">
        <text class="popup-title">猜您想问</text>
        <view class="close-btn" @click="closeQuestionPopup">
          <wd-icon name="close" size="20px" color="#666"></wd-icon>
        </view>
      </view>
      <view class="popup-content">
        <scroll-view scroll-y class="question-scroll">
          <view
            v-for="(item, index) in questionList"
            :key="index"
            class="question-item"
            @click="handleQuestionClick(item)"
          >
            <view class="c-#589BFF text-24rpx line-height-8">{{ item.question }}</view>
          </view>
        </scroll-view>
      </view>
    </view>
  </wd-popup> -->
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { querySimpleDataB, queryTypeB, generateUserAccessToken } from '@/service/customerService'
import kf from '@/static/my/customerService/kf.png'
import chatInputBar from '@/components/chat-input-bar/chat-input-bar.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const listTopData = ref<any>([])
const questionType = ref<any>(1)
const questionList = ref<any>([])
const showQuestionPopup = ref(false)
const isExpanded = ref(false) // 控制问题列表展开/收起状态

// 聊天相关状态
const chatMessages = ref<any>([])
const chatInput = ref('')
const currentQuestion = ref<any>(null)
const isAnswering = ref(false) // 新增：判断是否正在回复
const userQuestionCount = ref(0) // 新增：用户提问次数计数器
// 新增：跟踪每个问题的点击次数
const questionClickCount = ref(new Map<string, number>())

const params = reactive({
  orderBy: {},
  entity: {
    id: '3',
  },
  page: 1,
  size: 50,
})

// 获取z-paging的ref
const pagingRef = ref()
const keyboardHeight = ref(0)
const userAccessToken = ref('')

// 问题分类
const defaultCategory = async () => {
  const res: any = await querySimpleDataB()
  if (res.code === 0) {
    // const slicedData = res.data.slice(0, 4)
    listTopData.value = res.data
      .filter((item) => [3, 5, 7, 9].includes(item.type)) // 只保留指定 type
      .map((item) => {
        let questionName = ''
        let icon = null

        switch (item.type) {
          case 3:
            questionName = '岗位问题'
            icon = 1
            break
          case 5:
            questionName = '支付问题'
            icon = 2
            break
          case 7:
            questionName = '道具问题'
            icon = 3
            break
          case 9:
            questionName = '隐私问题'
            icon = 4
            break
        }
        return {
          ...item,
          questionName,
          icon,
        }
      })
    queryAllByType()
  }
}

// 计算显示的问题列表
const displayedQuestions = computed(() => {
  if (isExpanded.value) {
    return questionList.value
  }
  return questionList.value.slice(0, 5)
})

// 切换问题列表展开/收起状态
const toggleQuestionDisplay = () => {
  isExpanded.value = !isExpanded.value
}

// 分类下的问题
const queryAllByType = async () => {
  const res: any = await queryTypeB({ ...params })
  if (res.code === 0) {
    questionList.value = res.data.list
  }
}

// 切换问题类型
const handleQuestionType = (value: any) => {
  questionType.value = value
  params.entity.id = value
  queryAllByType()
}

// 显示更多问题弹窗
// const showMoreQuestions = async () => {
//   const url = `https://chatbot.aliyuncs.com/intl/index.htm?from=pnED3Ks4Pj&locale=zh-CN&_user_access_token=${userAccessToken.value}`

//   uni.navigateTo({
//     url: '/loginSetting/CustomerService/index?url=' + encodeURIComponent(url),
//   })
// }

// 获取当前用户token
const getUserToken = async () => {
  const res: any = await generateUserAccessToken()
  if (res.code === 0) {
    userAccessToken.value = res.data
  }
}

// 关闭问题弹窗
// const closeQuestionPopup = () => {
//   showQuestionPopup.value = false
// }

// 点击问题处理
const handleQuestionClick = (item: any) => {
  currentQuestion.value = item
  // 关闭问题弹窗
  showQuestionPopup.value = false

  // 增加用户提问次数
  userQuestionCount.value++

  // 新增：跟踪同一问题的点击次数
  const questionKey = item.question || item.id || JSON.stringify(item)
  const currentClickCount = questionClickCount.value.get(questionKey) || 0
  const newClickCount = currentClickCount + 1
  questionClickCount.value.set(questionKey, newClickCount)

  // 添加新的聊天消息（保留之前的消息）
  chatMessages.value.push(
    {
      content: item.question,
      isUser: true,
      timestamp: new Date().getTime(),
      questionIndex: userQuestionCount.value,
      questionKey, // 新增：记录问题标识
      clickCount: newClickCount, // 新增：记录当前点击次数
    },
    {
      content: item.answer,
      isUser: false,
      timestamp: new Date().getTime() + 1000,
      questionIndex: userQuestionCount.value,
      questionKey, // 新增：记录问题标识
      clickCount: newClickCount, // 新增：记录当前点击次数
    },
  )

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 发送消息
// const sendMessage = (msg: string) => {
//   if (!msg) return
//   // 增加用户提问次数
//   userQuestionCount.value++
//   chatMessages.value.push({
//     content: msg,
//     isUser: true,
//     timestamp: Date.now(),
//     questionIndex: userQuestionCount.value,
//   })
//   isAnswering.value = true
//   setTimeout(() => {
//     chatMessages.value.push({
//       content: '感谢您的咨询，我们会尽快为您处理相关问题。如有其他疑问，请随时联系我们。',
//       isUser: false,
//       timestamp: new Date().getTime(),
//       questionIndex: userQuestionCount.value,
//     })
//     isAnswering.value = false
//     // 客服回复后也滚动到底部
//     nextTick(() => {
//       scrollToBottom()
//     })
//   }, 1000)
// }

// 键盘高度变化处理
const keyboardHeightChange = (res: any) => {
  keyboardHeight.value = res.height
  if (res.height > 0) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 键盘隐藏处理
const hidedKeyboard = () => {
  console.log('键盘隐藏')
}

// 滚动到底部
function scrollToBottom() {
  pagingRef.value?.scrollToBottom()
}

// 转人工客服
const transferToHuman = async () => {
  try {
    const url = `https://chatbot.aliyuncs.com/intl/index.htm?from=pnED3Ks4Pj&locale=zh-CN&_user_access_token=${userAccessToken.value}`

    uni.navigateTo({
      url: '/loginSetting/CustomerService/index?url=' + encodeURIComponent(url),
    })
  } catch (error) {
    console.log('🚀 ~ transferToHuman ~ error:', error)
  }
}

watch(chatMessages, async () => {
  await nextTick()
  scrollToBottom()
})

onLoad(() => {
  getUserToken()
  defaultCategory()
})
</script>

<style lang="scss" scoped>
::v-deep .wd-input {
  width: 100%;
  background-color: transparent !important;
}

::v-deep .wd-input__placeholder {
  font-size: 28rpx !important;
  color: #888888 !important;
}

::v-deep .wd-input__inner {
  font-size: 28rpx !important;
  font-weight: 500;
  color: #888888 !important;
}

.font-fameliy {
  font-family: 'Reem Kufi Fun-SemiBold';
}

.btn_box {
  padding: 20rpx 40rpx;
  background: #e8e8e8;
  border-radius: 30rpx;
}

.input-container {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.send-btn {
  flex-shrink: 0;
  height: 60rpx;
  padding: 0 20rpx;
}
/* 聊天消息区域样式 */
.chat-messages-container {
  margin-top: 40rpx;
}

.message-item {
  margin-bottom: 30rpx;
}

.message-container {
  display: flex;
  gap: 20rpx;
  align-items: flex-start;
}

.message-container.message-user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.avatar-img {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.message-bubble {
  max-width: 70%;
  padding: 20rpx 24rpx;
  line-height: 1.4;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.message-bubble.bubble-user {
  background: #589bff;
}

.message-text {
  font-size: 28rpx;
  font-weight: normal;
  line-height: 44rpx;
  color: #333;
  word-wrap: break-word;
}

.question-text {
  line-height: 32rpx !important;
}

.bubble-user .message-text {
  color: #fff;
}
/* 转人工按钮样式 */
.transfer-human-btn {
  padding: 20rpx 0;
  margin-top: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.transfer-human-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.transfer-text {
  font-size: 28rpx;
  font-weight: 400;
  color: #589bff;
}
/* 底部锚点 */
.chat-bottom-anchor {
  width: 100%;
  height: 20rpx;
}

.pageContaner {
  padding: 0rpx 40rpx 0rpx;

  .pageContaner-card {
    width: 100%;
    height: 200rpx;
    background-image: url('@/static/my/customerService/bg.png');
    background-position: 100% 100%;
    background-size: 100% 100%;
  }

  .pageContaner-botto-card {
    padding: 40rpx 40rpx;
    background-color: #fff;
    border-radius: 30rpx;

    .border-bottom {
      border-bottom: 1rpx solid #ebebeb;
    }
  }
}
/* 弹窗样式 */
.question-popup {
  max-height: 80vh;
  overflow: hidden;
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
}

.popup-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  background: #fff;
  border-bottom: 2rpx solid #f5f5f5;
}

.popup-title {
  position: absolute;
  left: 100rpx;
  font-size: 32rpx;
  font-weight: normal;
  color: #333;
  transform: translateX(-50%);
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin-left: auto;
  cursor: pointer;
}

.popup-content {
  height: 60vh;
  background: #fff;
}

.question-scroll {
  height: 100%;
  padding: 0 40rpx;
}

.question-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.question-item:last-child {
  border-bottom: none;
}
</style>
