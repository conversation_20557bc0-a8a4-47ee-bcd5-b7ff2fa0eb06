<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="消息设置"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between border-b" @click="goRecommend">
        <view class="text-32rpx">个性化推荐</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between border-b" @click="goCommonPhrases">
        <view class="text-32rpx">设置常用语/招呼语</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <!-- <view class="setting-list flex-between border-b" @click="goCommonPhrases">
        <view class="text-32rpx">设置招呼语</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view> -->
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const goCommonPhrases = () => {
  uni.navigateTo({
    url: '/sub_common/pages/phrases/index?type=edit',
  })
}
const goRecommend = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/message/recommend',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
</style>
