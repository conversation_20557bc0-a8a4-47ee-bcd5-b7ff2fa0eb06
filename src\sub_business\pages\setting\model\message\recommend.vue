<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="个性化推荐"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">个性化推荐</view>
        <wd-switch
          v-model="recommendStatus"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
          @change="handleChange"
        />
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getRecommendStatus, setRecommendStatus } from '@/utils/storage'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const recommendStatus = ref(false)
onMounted(() => {
  recommendStatus.value = getRecommendStatus()
})
const handleChange = (e: any) => {
  console.log(e)
  recommendStatus.value = e.value
  setRecommendStatus(e.value)
}
</script>
<style scoped lang="scss">
.setting {
  padding: 20rpx 20rpx;
  .setting-list {
    padding: 30rpx 20rpx;
    .list-item-text {
      color: #333;
    }
  }
}
</style>
