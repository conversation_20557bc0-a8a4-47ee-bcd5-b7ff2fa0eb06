<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '发票记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">发票记录</text>
          </template>
        </wd-navbar>
      </wd-config-provider>

      <!-- 使用wotUI的tabs组件 -->
      <view class="tabs-wrapper">
        <wd-tabs
          v-model="activeTab"
          :line-width="40"
          color="#000000"
          custom-class="custom-tabs"
          inactive-color="#555555"
          line-height="2"
          slidable="always"
          @change="handleTabChange"
        >
          <wd-tab name="all" title="全部"></wd-tab>
          <wd-tab name="oneMonth" title="近一月"></wd-tab>
          <wd-tab name="threeMonths" title="近三月"></wd-tab>
        </wd-tabs>
      </view>
    </template>

    <view class="recharge-details-box">
      <!-- 充值明细列表 -->
      <view
        v-for="(item, index) in pageData"
        :key="index"
        class="detail-item"
        @click="handlePayProp(item)"
      >
        <view class="item-row">
          <view class="item-left">
            <view class="item-title">发票记录</view>
            <view class="item-time">{{ item.createTime }}</view>
          </view>
          <view class="item-right">
            <text class="item-amount">{{ item.invoiceMoney }}</text>
            <text class="item-unit">元</text>
          </view>
        </view>
      </view>
    </view>
  </z-paging>

  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { getInvoiceRecordList } from '@/service/walletInvoice'
import { Money, toFixed } from '@/utils/precision'

const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

const activeTab = ref('all')
const listInfo = ref([])

const formatDateTime = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const initDefaultTimeRange = () => {
  const now = new Date()
  const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate())

  sixMonthsAgo.setHours(0, 0, 0, 0)
  now.setHours(23, 59, 59, 0)

  return {
    startTime: formatDateTime(sixMonthsAgo),
    endTime: formatDateTime(now),
  }
}

function handleTabChange(name: string) {
  activeTab.value = name

  const now = new Date()

  switch (name) {
    case 'all': {
      const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate())
      sixMonthsAgo.setHours(0, 0, 0, 0)
      now.setHours(23, 59, 59, 0)
      params.value.entity.startTime = formatDateTime(sixMonthsAgo)
      params.value.entity.endTime = formatDateTime(now)
      break
    }
    case 'oneMonth': {
      const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
      const endTime = new Date()
      oneMonthAgo.setHours(0, 0, 0, 0)
      endTime.setHours(23, 59, 59, 0)
      params.value.entity.startTime = formatDateTime(oneMonthAgo)
      params.value.entity.endTime = formatDateTime(endTime)
      break
    }
    case 'threeMonths': {
      const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
      const endTime = new Date()
      threeMonthsAgo.setHours(0, 0, 0, 0)
      endTime.setHours(23, 59, 59, 0)
      params.value.entity.startTime = formatDateTime(threeMonthsAgo)
      params.value.entity.endTime = formatDateTime(endTime)
      break
    }
  }

  pagingRef.value?.reload()
}

const params = ref({
  entity: initDefaultTimeRange(),
  page: 1,
  size: 10,
})

const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await getInvoiceRecordList({
    ...params.value,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    listInfo.value = res.data.list.map((item) => {
      if (item.invoiceMoney) {
        item.invoiceMoney = toFixed(Money.centToYuan(item.invoiceMoney), 2)
      }
      return item
    })
  }

  pagingRef.value.complete(listInfo.value)
}

const handlePayProp = (item) => {
  uni.navigateTo({
    url: '/sub_business/pages/walletInvoice/InvoiceApplication/index?id=' + item.id,
  })
}

function handleClickLeft() {
  uni.navigateBack()
}

onLoad(() => {
  pagingRef.value?.reload()
})

onMounted(() => {
  pagingRef.value?.reload()
})

onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
.header-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 60rpx 0;
  background: transparent;
}

.header-title {
  font-size: 48rpx;
  font-weight: 500;
  color: #000000;
}

.header-action {
  padding-left: 40rpx;
  font-size: 28rpx;
  color: #000000;
  cursor: pointer;
}

.tabs-wrapper {
  height: 148rpx;
  padding: 30rpx 60rpx;
  margin-top: 54rpx;
  font-size: 32rpx;
  background: #ffffff;
  border-bottom: 2rpx solid #d7d7d7;
  border-radius: 60rpx 60rpx 0 0;
}

:deep(.wd-tabs__container) {
  height: 0;
}

:deep(.custom-tabs) {
  .wd-tab {
    padding: 32rpx 0;
    font-size: 28rpx;
  }
}

:deep(.wd-tabs__nav-item) {
  font-size: 32rpx !important;
  transition: font-size 0.3s ease;
}

:deep(.wd-tabs__nav-item.is-active) {
  font-size: 32rpx !important;
}

.recharge-details-box {
  padding: 0 60rpx 96rpx;
  background: #ffffff;
}

:deep(.zp-empty-view-center) {
  background: #ffffff !important;
}

:deep(.zp-paging-container) {
  background: #ffffff !important;
}

.item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  background: #fff;
  border-bottom: 1rpx solid #ededed;
}

.item-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.item-time {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #888;
}

.item-right {
  display: flex;
  align-items: baseline;
}

.item-amount {
  font-size: 40rpx;
  font-weight: bold;
  color: #000;
}

.item-unit {
  margin-left: 2rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: #000;
}

.bottom-section {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.total-info {
  display: flex;
  flex: 1;
  align-items: baseline;
}

.total-label {
  font-size: 28rpx;
  color: #333333;
}

.total-amount {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.total-count {
  margin-right: 20rpx;
  font-size: 24rpx;
  color: #999999;
}

.next-button {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
  background-color: #007aff;
  border-radius: 8rpx;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #888888;
}
</style>
