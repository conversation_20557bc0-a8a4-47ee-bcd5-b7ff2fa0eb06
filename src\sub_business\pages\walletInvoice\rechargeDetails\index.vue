<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '充值明细',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">充值明细</text>
          </template>
        </wd-navbar>
      </wd-config-provider>

      <!-- 使用wotUI的tabs组件 -->
      <view class="tabs-wrapper">
        <wd-tabs
          v-model="activeTab"
          :line-width="40"
          color="#000000"
          custom-class="custom-tabs"
          inactive-color="#555555"
          line-height="2"
          slidable="always"
          @change="handleTabChange"
        >
          <wd-tab name="all" title="全部"></wd-tab>
          <wd-tab name="recharge" title="充值记录"></wd-tab>
          <wd-tab name="usage" title="使用记录"></wd-tab>
        </wd-tabs>
      </view>
    </template>

    <view class="recharge-details-box">
      <!-- 充值明细列表 -->
      <view v-for="(item, index) in pageData" :key="index" class="detail-item">
        <view class="item-content">
          <view class="item-left">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-time">{{ item.createTime }}</view>
          </view>
          <view class="item-right">
            <view :class="{ 'amount-negative': item.amount < 0 }" class="item-amount">
              {{ item.amount > 0 ? '+' : '' }}{{ formatAmount(item.amount) }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { Money } from '@/utils/precision'

// 分页相关
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

// 标签页切换
const activeTab = ref('all')

// 金额格式化函数
function formatAmount(amount: number | string): string {
  const numAmount = Number(amount)
  if (isNaN(numAmount)) return '0.00'

  // 这里的模拟数据是以元为单位的，所以直接格式化显示
  return Money.format(Math.abs(numAmount))
}

// 标签页切换处理
function handleTabChange(name: string) {
  activeTab.value = name
  pagingRef.value?.reload()
}

// 返回按钮
function handleClickLeft() {
  uni.navigateBack()
}

// 模拟数据查询 - 实际项目中应该调用真实API
const queryList = async (page, size) => {
  // 模拟API请求延迟
  pageSetInfo(page, size)
  await new Promise((resolve) => setTimeout(resolve, 100))

  // 模拟数据
  const mockData = [
    {
      id: 1,
      title: '基础岗位发布-电商运营',
      createTime: '2024-6-20 10:57:54',
      amount: -88,
      type: 'usage',
    },
    {
      id: 2,
      title: '基础岗位发布-全栈开发工程师',
      createTime: '2024-6-20 10:57:54',
      amount: -88,
      type: 'usage',
    },
    {
      id: 3,
      title: '基础岗位发布-行政助理',
      createTime: '2025-6-20 10:57:54',
      amount: -88,
      type: 'usage',
    },
    {
      id: 4,
      title: '账户充值',
      createTime: '2025-6-1 15:30:20',
      amount: 100,
      type: 'recharge',
    },
  ]

  // 根据标签筛选数据
  let filteredData = mockData
  if (activeTab.value === 'recharge') {
    filteredData = mockData.filter((item) => item.amount > 0)
  } else if (activeTab.value === 'usage') {
    filteredData = mockData.filter((item) => item.amount < 0)
  }

  // 更新分页数据
  pagingRef.value.complete(filteredData)
}

onLoad(() => {
  pagingRef.value?.reload()
})
onMounted(() => {
  pagingRef.value?.reload()
})

onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
// tabs容器样式
.tabs-wrapper {
  height: 148rpx;
  padding: 30rpx 60rpx;
  margin-top: 54rpx;
  font-size: 32rpx;
  background: #ffffff;
  border-bottom: 2rpx solid #d7d7d7;
  border-radius: 32rpx 32rpx 0 0;
}

:deep(.wd-tabs__container) {
  height: 0;
}

// 自定义tabs样式
:deep(.custom-tabs) {
  .wd-tab {
    padding: 32rpx 0;
    font-size: 28rpx;
  }
}
/* 标签页导航项字体大小控制 */
:deep(.wd-tabs__nav-item) {
  font-size: 28rpx !important; /* 未选中时的字体大小 */
  transition: font-size 0.3s ease;
}

:deep(.wd-tabs__nav-item.is-active) {
  font-size: 32rpx !important; /* 选中时的字体大小 */
}

// 主容器样式
.recharge-details-box {
  // min-height: calc(100vh - 220rpx);
  padding: 0 60rpx;
  background: #ffffff;
}

:deep(.zp-empty-view-center) {
  background: #ffffff !important;
}

:deep(.zp-paging-container) {
  background: #ffffff !important;
}

// 列表项样式
.detail-item {
  border-bottom: 2rpx solid #d7d7d7;

  &:last-child {
    border-bottom: none;
  }
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 0;
}

.item-left {
  flex: 1;
}

.item-title {
  margin-bottom: 16rpx;
  font-size: 32rpx;
  font-weight: normal;
  color: #333333;
}

.item-time {
  font-size: 28rpx;
  color: #888888;
}

.item-right {
  text-align: right;
}

.item-amount {
  font-size: 36rpx;
  font-weight: 500;
  color: #000000;
}

.amount-negative {
  color: #333333;
}

// 空状态样式
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #888888;
}
</style>
