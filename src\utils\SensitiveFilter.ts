export class SensitiveFilter {
  private sensitiveWords: Set<string> = new Set()
  private trieRoot: TrieNode = new TrieNode()
  private ignoreChars = new Set([
    ' ',
    '\t',
    '\n',
    '\r',
    '.',
    ',',
    '!',
    '?',
    ';',
    ':',
    '-',
    '_',
    '=',
    '+',
    '|',
    '\\',
    '*',
    '#',
    '@',
    '$',
    '%',
    '^',
    '&',
    '(',
    ')',
    '[',
    ']',
    '{',
    '}',
    '<',
    '>',
    '/',
    '~',
    '`',
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '。',
    '，',
    '！',
    '？',
    '；',
    '：',
    '、',
    '"',
    '"',
    "'",
    "'",
    '（',
    '）',
    '【',
    '】',
    '《',
    '》',
  ])

  constructor(words: string[]) {
    this.buildTrie(words)
  }

  private buildTrie(words: string[]) {
    for (const word of words) {
      if (word.trim()) {
        const cleanWord = this.normalizeText(word.trim())
        this.sensitiveWords.add(cleanWord)
        this.insertWord(cleanWord)
      }
    }
  }

  private insertWord(word: string) {
    let node = this.trieRoot
    for (const char of word) {
      if (!node.children.has(char)) {
        node.children.set(char, new TrieNode())
      }
      node = node.children.get(char)!
    }
    node.isEnd = true
    node.originalLength = word.length
  }

  /**
   * 标准化文本 - 移除干扰字符，转换为小写
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .split('')
      .filter((char) => !this.ignoreChars.has(char))
      .join('')
  }

  /**
   * 获取字符在原文本中的实际位置映射
   */
  private getCharacterMapping(originalText: string): { normalizedText: string; mapping: number[] } {
    const mapping: number[] = []
    const normalizedChars: string[] = []

    for (let i = 0; i < originalText.length; i++) {
      const char = originalText[i].toLowerCase()
      if (!this.ignoreChars.has(char)) {
        normalizedChars.push(char)
        mapping.push(i)
      }
    }

    return {
      normalizedText: normalizedChars.join(''),
      mapping,
    }
  }

  /**
   * 增强版文本过滤 - 支持模糊匹配
   */
  public filterText(text: string, replacement: string = '*'): string {
    if (!text) return text

    const { normalizedText, mapping } = this.getCharacterMapping(text)
    const matches = this.findAllMatches(normalizedText)

    if (matches.length === 0) return text

    // 按照原文位置标记需要替换的字符
    const shouldReplace = new Array(text.length).fill(false)

    for (const match of matches) {
      const startPos = mapping[match.start]
      const endPos = match.end < mapping.length ? mapping[match.end - 1] : text.length - 1

      // 标记整个范围内的字符都需要替换
      for (let i = startPos; i <= endPos; i++) {
        shouldReplace[i] = true
      }
    }

    // 构建替换后的文本
    let result = ''
    for (let i = 0; i < text.length; i++) {
      if (shouldReplace[i]) {
        result += replacement
      } else {
        result += text[i]
      }
    }

    return result
  }

  /**
   * 查找所有匹配的敏感词位置
   */
  private findAllMatches(
    normalizedText: string,
  ): Array<{ start: number; end: number; word: string }> {
    const matches: Array<{ start: number; end: number; word: string }> = []

    for (let i = 0; i < normalizedText.length; i++) {
      const match = this.findSensitiveWordAt(normalizedText, i)
      if (match.found) {
        matches.push({
          start: i,
          end: i + match.length,
          word: normalizedText.substring(i, i + match.length),
        })
        i += match.length - 1 // 跳过已匹配的部分
      }
    }

    return matches
  }

  /**
   * 从指定位置查找敏感词
   */
  private findSensitiveWordAt(
    text: string,
    startIndex: number,
  ): { found: boolean; length: number } {
    let node = this.trieRoot
    let length = 0
    let maxMatchLength = 0

    for (let i = startIndex; i < text.length; i++) {
      const char = text[i]

      if (!node.children.has(char)) {
        break
      }

      node = node.children.get(char)!
      length++

      if (node.isEnd) {
        maxMatchLength = length // 记录最长匹配
      }
    }

    return { found: maxMatchLength > 0, length: maxMatchLength }
  }

  /**
   * 检查是否包含敏感词（增强版）
   */
  public containsSensitiveWord(text: string): boolean {
    if (!text) return false

    const { normalizedText } = this.getCharacterMapping(text)
    const matches = this.findAllMatches(normalizedText)

    return matches.length > 0
  }

  /**
   * 获取文本中的所有敏感词（增强版）
   */
  public getSensitiveWords(
    text: string,
  ): Array<{ word: string; original: string; position: { start: number; end: number } }> {
    const result: Array<{
      word: string
      original: string
      position: { start: number; end: number }
    }> = []
    if (!text) return result

    const { normalizedText, mapping } = this.getCharacterMapping(text)
    const matches = this.findAllMatches(normalizedText)

    for (const match of matches) {
      const startPos = mapping[match.start]
      const endPos = match.end < mapping.length ? mapping[match.end - 1] + 1 : text.length

      result.push({
        word: match.word,
        original: text.substring(startPos, endPos),
        position: { start: startPos, end: endPos },
      })
    }

    return result
  }

  /**
   * 严格模式检测 - 检测变形词汇
   */
  public filterTextStrict(text: string, replacement: string = '*'): string {
    let result = this.filterText(text, replacement)

    // 额外检测一些常见的变形模式
    result = this.handleVariations(result, replacement)

    return result
  }

  /**
   * 处理常见的敏感词变形
   */
  private handleVariations(text: string, replacement: string): string {
    // 这里可以添加更多的变形检测逻辑
    const variations = [
      // 拼音变形
      { pattern: /cao\s*ni\s*ma/gi, replacement: replacement.repeat(6) },
      { pattern: /sb\s*[0-9]*\s*/gi, replacement: replacement.repeat(2) },
      // 谐音变形
      { pattern: /草\s*泥\s*马/g, replacement: replacement.repeat(3) },
      // 数字干扰
      { pattern: /傻\s*[0-9]*\s*逼/g, replacement: replacement.repeat(2) },
    ]

    let result = text
    for (const variation of variations) {
      result = result.replace(variation.pattern, variation.replacement)
    }

    return result
  }
}

class TrieNode {
  children: Map<string, TrieNode> = new Map()
  isEnd: boolean = false
  originalLength: number = 0
}
